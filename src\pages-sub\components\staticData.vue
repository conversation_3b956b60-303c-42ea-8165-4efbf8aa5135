<template>
  <!-- 常规数据卡片 (Material Design) -->
  <view
    class="p-[24rpx] my-[16rpx] bg-white rounded-lg shadow-md"
    v-if="![HealthType.BLOOD_PRESSURE].includes(type as HealthType)"
  >
    <view class="mb-[16rpx]">
      <view class="text-gray-900 font-medium text-base">概览数据</view>
      <view class="text-gray-500 text-xs" v-if="unit">({{ unit }})</view>
    </view>

    <view class="grid gap-4 py-[16rpx] my-[16rpx] grid-cols-3">
      <view class="text-center">
        <view class="text-lg font-medium text-gray-900">
          {{ curData.maxV || 0 }}
        </view>
        <view class="text-xs text-gray-600">最高</view>
      </view>
      <view class="text-center">
        <view class="text-lg font-medium text-gray-900">
          {{ curData.minV || 0 }}
        </view>
        <view class="text-xs text-gray-600">最低</view>
      </view>
      <view class="text-center">
        <view class="text-lg font-medium text-gray-900">
          {{ curData.avg || 0 }}
        </view>
        <view class="text-xs text-gray-600">平均</view>
      </view>
    </view>

    <view class="py-[8rpx] border-t border-gray-200"></view>

    <view
      class="mt-[16rpx] grid grid-cols-4 gap-2 justify-center"
      v-if="
        ![HealthType.VITAL_INDEX, HealthType.HEART_INDEX, HealthType.MOOD].includes(
          type as HealthType,
        )
      "
    >
      <view class="flex items-center justify-center">
        <view class="w-[12rpx] h-[12rpx] rounded-full bg-blue-500 mr-[8rpx]"></view>
        <text class="text-xs text-gray-900">{{ titleName }}过低:</text>
        <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.less }}</text>
      </view>
      <view class="flex items-center justify-center">
        <view class="w-[12rpx] h-[12rpx] rounded-full bg-red-500 mr-[8rpx]"></view>
        <text class="text-xs text-gray-900">{{ titleName }}过高:</text>
        <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.over }}</text>
      </view>
      <view class="flex items-center col-span-2">
        <view class="w-[12rpx] h-[12rpx] rounded-full bg-gray-500 mr-[8rpx]"></view>
        <text class="text-xs text-gray-900">{{ titleName }}范围:</text>
        <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.range }}</text>
      </view>
    </view>

    <view
      class="mt-[16rpx] gap-2 px-4 items-center justify-center border-t-(1px solid gray-100) pt-4"
      v-if="curData.hasOwnProperty('normalCount')"
      :class="type !== HealthType.MOOD ? 'grid grid-cols-4' : ''"
    >
      <view class="flex items-center" v-if="type === HealthType.MOOD">
        <view class="w-[12rpx] h-[12rpx] rounded-full bg-blue-500 mr-[8rpx]"></view>
        <!-- <text class="text-xs text-gray-900">重度</text> -->
        <text class="text-sm text-gray-700 ml-[4rpx]">比例越高代表此项情绪越高</text>
      </view>
      <template v-else>
        <view class="flex items-center justify-center">
          <view class="w-[12rpx] h-[12rpx] rounded-full bg-blue-500 mr-[8rpx]"></view>
          <text class="text-xs text-gray-900">正常</text>
          <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.normalCount }}</text>
        </view>
        <view class="flex items-center justify-center">
          <view class="w-[12rpx] h-[12rpx] rounded-full bg-red-500 mr-[8rpx]"></view>
          <text class="text-xs text-gray-900">轻度</text>
          <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.mildCount }}</text>
        </view>
        <view class="flex items-center justify-center">
          <view class="w-[12rpx] h-[12rpx] rounded-full bg-yellow-500 mr-[8rpx]"></view>
          <text class="text-xs text-gray-900">中度</text>
          <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.moderateCount }}</text>
        </view>
        <view class="flex items-center justify-center">
          <view class="w-[12rpx] h-[12rpx] rounded-full bg-red-500 mr-[8rpx]"></view>
          <text class="text-xs text-gray-900">重度</text>
          <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.severeCount }}</text>
        </view>
      </template>
    </view>
  </view>

  <!-- 血压数据卡片 (Material Design) -->
  <view class="p-[24rpx] my-[16rpx] bg-white rounded-lg shadow-md" v-else>
    <!-- 标题栏 -->
    <view class="mb-[24rpx]">
      <view class="text-gray-900 font-medium text-base">概览数据</view>
      <view class="text-gray-500 text-xs">({{ unit }})</view>
    </view>
    <!-- 收缩压 -->
    <view class="mb-[16rpx]">
      <view class="pb-[8rpx] border-b border-gray-100">
        <view class="text-sm font-medium text-primary-600">收缩压</view>
      </view>

      <view class="flex justify-between items-center mt-[16rpx]">
        <view class="text-center flex-1">
          <view class="text-lg font-medium text-gray-900">{{ curData.sbp.avg }}</view>
          <view class="text-xs text-gray-600 mt-[4rpx]">平均</view>
        </view>
        <view class="h-[36rpx] w-[1rpx] bg-gray-200"></view>
        <view class="text-center flex-1">
          <view class="text-lg font-medium text-red-500">{{ curData.sbp.less }}</view>
          <view class="text-xs text-gray-600 mt-[4rpx]">过低</view>
        </view>
        <view class="h-[36rpx] w-[1rpx] bg-gray-200"></view>
        <view class="text-center flex-1">
          <view class="text-lg font-medium text-red-500">{{ curData.sbp.over }}</view>
          <view class="text-xs text-gray-600 mt-[4rpx]">过高</view>
        </view>
      </view>

      <view class="flex items-center mt-[16rpx] px-[8rpx] py-[12rpx] bg-gray-50 rounded-[4rpx]">
        <view class="w-[12rpx] h-[12rpx] rounded-full bg-primary-500 mr-[8rpx]"></view>
        <text class="text-xs text-gray-900">范围:</text>
        <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.sbp.range }}</text>
      </view>
    </view>

    <!-- 分隔线 -->
    <view class="h-[1rpx] bg-gray-200 my-[16rpx]"></view>

    <!-- 舒缩压 -->
    <view class="mb-[24rpx]">
      <view class="pb-[8rpx] border-b border-gray-100">
        <view class="text-sm font-medium text-primary-600">舒缩压</view>
      </view>

      <view class="flex justify-between items-center mt-[16rpx]">
        <view class="text-center flex-1">
          <view class="text-lg font-medium text-gray-900">{{ curData.dbp.avg }}</view>
          <view class="text-xs text-gray-600 mt-[4rpx]">平均</view>
        </view>
        <view class="h-[36rpx] w-[1rpx] bg-gray-200"></view>
        <view class="text-center flex-1">
          <view class="text-lg font-medium text-red-500">{{ curData.dbp.less }}</view>
          <view class="text-xs text-gray-600 mt-[4rpx]">过低</view>
        </view>
        <view class="h-[36rpx] w-[1rpx] bg-gray-200"></view>
        <view class="text-center flex-1">
          <view class="text-lg font-medium text-red-500">{{ curData.dbp.over }}</view>
          <view class="text-xs text-gray-600 mt-[4rpx]">过高</view>
        </view>
      </view>

      <view class="flex items-center mt-[16rpx] px-[8rpx] py-[12rpx] bg-gray-50 rounded-[4rpx]">
        <view class="w-[12rpx] h-[12rpx] rounded-full bg-primary-500 mr-[8rpx]"></view>
        <text class="text-xs text-gray-900">范围:</text>
        <text class="text-xs text-gray-700 ml-[4rpx]">{{ curData.dbp.range }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { analyzeArray, HealthType } from '../smartDevice/shared'
import { useHealthCharts } from '../smartDevice/useCharts'
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  unit: {
    type: String,
    required: true,
  },
  tab: {
    type: Number,
    required: false,
  },
  type: {
    required: false,
  },
})
const { historyHealthData } = useHealthCharts()

const curData = computed(() => {
  // console.log(props.tab, props.type === HealthType.VITAL_INDEX)
  if (props.type === HealthType.MOOD) {
    const flatArray = Object.values(props.data.log).map((num) => {
      return num[props.tab] * 100
    }) as any // 转换为百分比
    const { max, min, avg, normalCount, mildCount, moderateCount, severeCount } = analyzeArray(
      flatArray,
      props.tab,
      2,
    )
    console.log(max, min, avg, normalCount, mildCount, moderateCount, severeCount)
    console.log(props.data)
    return { maxV: max, minV: min, avg, normalCount, mildCount, moderateCount, severeCount }
  }
  if (props.type === HealthType.VITAL_INDEX) {
    const index = props.tab
    console.log(props.data)

    const { log = {} } =
      index === 0 ? props.data.pressure : index === 1 ? props.data.anxiety : props.data.depression

    // const log = index== 0 ? props.data
    const { max, min, avg, normalCount, mildCount, moderateCount, severeCount } = analyzeArray(
      Object.values(log),
      index,
      0,
    )
    console.log(max, min, avg, normalCount, mildCount, moderateCount, severeCount)

    return {
      maxV: max,
      minV: min,
      avg: parseInt(avg as any),
      normalCount,
      mildCount,
      moderateCount,
      severeCount,
    }
  }
  if (props.type === HealthType.HEART_INDEX) {
    const index = props.tab
    console.log(index)
    const { log = {} } = index === 0 ? props.data.af : index === 1 ? props.data.sa : props.data.bv

    const { max, min, avg, normalCount, mildCount, moderateCount, severeCount } = analyzeArray(
      Object.values(log),
      index,
      0,
    )
    return {
      maxV: max,
      minV: min,
      avg: parseInt(avg as any),
      normalCount,
      mildCount,
      moderateCount,
      severeCount,
    }
  }
  return props.data
})

const titleName = computed(() => {
  if (props.type === HealthType.BLOOD_PRESSURE) {
    return '血压'
  }
  if (props.type === HealthType.MOOD) {
    return historyHealthData.mood.dates[props.tab]
  }
  if (props.type === HealthType.VITAL_INDEX) {
    return historyHealthData.psychology.dates[props.tab]
  }
  if (props.type === HealthType.HEART_INDEX) {
    return historyHealthData.physiology.name[props.tab]
  }
  return props.title
})
</script>

<style scoped>
/* 可以移除这个自定义样式，因为我们已经使用了 Tailwind */
</style>
