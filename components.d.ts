/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    JnChart: typeof import('./src/components/jn-chart/index.vue')['default']
    WdBadge: typeof import('wot-design-uni/components/wd-badge/wd-badge.vue')['default']
    WdButton: typeof import('wot-design-uni/components/wd-button/wd-button.vue')['default']
    WdCard: typeof import('wot-design-uni/components/wd-card/wd-card.vue')['default']
    WdCheckbox: typeof import('wot-design-uni/components/wd-checkbox/wd-checkbox.vue')['default']
    WdCircle: typeof import('wot-design-uni/components/wd-circle/wd-circle.vue')['default']
    WdCollapse: typeof import('wot-design-uni/components/wd-collapse/wd-collapse.vue')['default']
    WdCollapseItem: typeof import('wot-design-uni/components/wd-collapse-item/wd-collapse-item.vue')['default']
    WdConfigProvider: typeof import('wot-design-uni/components/wd-config-provider/wd-config-provider.vue')['default']
    WdForm: typeof import('wot-design-uni/components/wd-form/wd-form.vue')['default']
    WdIcon: typeof import('wot-design-uni/components/wd-icon/wd-icon.vue')['default']
    WdInput: typeof import('wot-design-uni/components/wd-input/wd-input.vue')['default']
    WdMessageBox: typeof import('wot-design-uni/components/wd-message-box/wd-message-box.vue')['default']
    WdPopup: typeof import('wot-design-uni/components/wd-popup/wd-popup.vue')['default']
    WdSearch: typeof import('wot-design-uni/components/wd-search/wd-search.vue')['default']
    WdSwiper: typeof import('wot-design-uni/components/wd-swiper/wd-swiper.vue')['default']
    WdTab: typeof import('wot-design-uni/components/wd-tab/wd-tab.vue')['default']
    WdTabs: typeof import('wot-design-uni/components/wd-tabs/wd-tabs.vue')['default']
    WdToast: typeof import('wot-design-uni/components/wd-toast/wd-toast.vue')['default']
  }
}
