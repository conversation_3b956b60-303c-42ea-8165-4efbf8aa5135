<route lang="json5">
{
  style: {
    navigationBarTitleText: '文件预览',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black'
  },
}
</route>

<template>
  <view class="file-preview-page">
    <!-- 图片预览 -->
    <ImagePreview 
      v-if="previewType === 'image'" 
      :images="imageFiles"
      :show-mask="false"
    />
    
    <!-- 视频预览 -->
    <VideoPreview 
      v-else-if="previewType === 'video'" 
      :video-file="currentFile"
      :show-info="true"
      :show-actions="true"
    />
    
    <!-- 文档预览 -->
    <DocumentPreview 
      v-else-if="previewType === 'document'" 
      :document-file="currentFile"
      :show-info="true"
      :preview-service="previewService"
    />
    
    <!-- 富文本预览 -->
    <RichTextPreview 
      v-else-if="previewType === 'richtext'" 
      :content="richTextContent"
      :url="richTextUrl"
      :title="pageTitle"
      :use-rich-text="true"
      :show-copy-button="true"
      :show-share-button="true"
    />
    
    <!-- 文件列表预览 -->
    <view v-else-if="previewType === 'list'" class="file-list">
      <view class="list-header">
        <text class="list-title">文件列表 ({{ fileList.length }})</text>
      </view>
      
      <view class="list-content">
        <view 
          v-for="(file, index) in fileList" 
          :key="file.id || index"
          class="file-item"
          @click="previewSingleFile(file, index)"
        >
          <view class="file-icon">
            <i :class="getFileIcon(file)" class="text-2xl"></i>
          </view>
          <view class="file-info">
            <text class="file-name">{{ file.name }}</text>
            <text class="file-meta">
              {{ getFileTypeName(file.type) }}
              <text v-if="file.size"> · {{ formatFileSize(file.size) }}</text>
            </text>
          </view>
          <view class="file-action">
            <i class="i-carbon-chevron-right text-gray-400"></i>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else class="error-state">
      <i class="i-carbon-warning text-6xl text-red-500"></i>
      <text class="error-text">不支持的预览类型</text>
      <wd-button type="primary" @click="goBack">
        返回
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { 
  ImagePreview, 
  VideoPreview, 
  DocumentPreview, 
  RichTextPreview,
  getFileType,
  previewFile
} from '@/components/file-preview'

// 页面参数
const previewType = ref<string>('')
const currentFile = ref<FileItem>({} as FileItem)
const fileList = ref<FileItem[]>([])
const imageFiles = ref<FileItem[]>([])
const richTextContent = ref('')
const richTextUrl = ref('')
const pageTitle = ref('文件预览')
const previewService = ref<'tencent' | 'microsoft' | 'google' | 'custom'>('microsoft')

// 页面加载
onLoad((options: any) => {
  console.log('文件预览页面参数:', options)
  
  previewType.value = options.type || ''
  
  switch (previewType.value) {
    case 'image':
      handleImagePreview(options)
      break
    case 'video':
      handleVideoPreview(options)
      break
    case 'document':
      handleDocumentPreview(options)
      break
    case 'richtext':
      handleRichTextPreview(options)
      break
    case 'list':
      handleFileListPreview(options)
      break
    default:
      console.error('未知的预览类型:', previewType.value)
  }
})

// 处理图片预览
const handleImagePreview = (options: any) => {
  if (options.url && options.name) {
    const imageFile: FileItem = {
      name: decodeURIComponent(options.name),
      url: decodeURIComponent(options.url),
      type: 'image',
      extension: options.extension
    }
    imageFiles.value = [imageFile]
    currentFile.value = imageFile
    pageTitle.value = imageFile.name
  }
}

// 处理视频预览
const handleVideoPreview = (options: any) => {
  if (options.url && options.name) {
    currentFile.value = {
      name: decodeURIComponent(options.name),
      url: decodeURIComponent(options.url),
      type: 'video',
      extension: options.extension,
      size: options.size ? parseInt(options.size) : undefined
    }
    pageTitle.value = currentFile.value.name
  }
}

// 处理文档预览
const handleDocumentPreview = (options: any) => {
  if (options.url && options.name) {
    currentFile.value = {
      name: decodeURIComponent(options.name),
      url: decodeURIComponent(options.url),
      type: 'document',
      extension: options.extension,
      size: options.size ? parseInt(options.size) : undefined
    }
    pageTitle.value = currentFile.value.name
    
    if (options.previewService) {
      previewService.value = options.previewService
    }
  }
}

// 处理富文本预览
const handleRichTextPreview = (options: any) => {
  if (options.content) {
    richTextContent.value = decodeURIComponent(options.content)
  } else if (options.url) {
    richTextUrl.value = decodeURIComponent(options.url)
  }
  
  if (options.title) {
    pageTitle.value = decodeURIComponent(options.title)
  }
}

// 处理文件列表预览
const handleFileListPreview = (options: any) => {
  if (options.files) {
    try {
      fileList.value = JSON.parse(decodeURIComponent(options.files))
      pageTitle.value = '文件列表'
    } catch (err) {
      console.error('解析文件列表失败:', err)
    }
  }
}

// 预览单个文件
const previewSingleFile = (file: FileItem, index: number) => {
  previewFile(file)
}

// 获取文件图标
const getFileIcon = (file: FileItem) => {
  const iconMap = {
    image: 'i-carbon-image text-blue-500',
    video: 'i-carbon-video text-red-500',
    document: 'i-carbon-document text-green-500',
    richtext: 'i-carbon-text-creation text-purple-500'
  }
  return iconMap[file.type] || 'i-carbon-document text-gray-500'
}

// 获取文件类型名称
const getFileTypeName = (type: FileType) => {
  const nameMap = {
    image: '图片',
    video: '视频',
    document: '文档',
    richtext: '富文本'
  }
  return nameMap[type] || '未知'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 设置页面标题
onMounted(() => {
  if (pageTitle.value) {
    uni.setNavigationBarTitle({
      title: pageTitle.value
    })
  }
})
</script>

<style scoped lang="scss">
.file-preview-page {
  min-height: 100vh;
  background: #fff;
  padding: 20rpx;
  
  .file-list {
    .list-header {
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;
      margin-bottom: 20rpx;
      
      .list-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }
    
    .list-content {
      .file-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 8rpx;
        margin-bottom: 16rpx;
        transition: all 0.3s ease;
        
        &:active {
          background: #e9ecef;
          transform: scale(0.98);
        }
        
        .file-icon {
          width: 80rpx;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;
          border-radius: 8rpx;
          margin-right: 20rpx;
        }
        
        .file-info {
          flex: 1;
          
          .file-name {
            display: block;
            font-size: 30rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .file-meta {
            font-size: 24rpx;
            color: #666;
          }
        }
        
        .file-action {
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  
  .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    
    .error-text {
      font-size: 32rpx;
      color: #666;
      margin: 20rpx 0 40rpx;
    }
  }
}
</style>
