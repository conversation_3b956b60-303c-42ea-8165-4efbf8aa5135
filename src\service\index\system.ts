/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
// @ts-ignore
import { CustomRequestOptions } from '@/interceptors/request'
import request from '@/utils/request'
/**
 * 发送短信验证码
 * @param data 请求体，包含手机号等信息
 * @param options 可选的自定义请求配置
 * @returns Promise<any>
 */
export function sendSmsCode(
  phone: string,

  options?: CustomRequestOptions,
) {
  return request<any>('/admin/common.SmsCode/sendSmsCode', {
    method: 'POST',
    data: {
      phone,
    },
    ...options,
  })
}
