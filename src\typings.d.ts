// 全局要用的类型放到这里

declare global {
  type IResData<T> = {
    code: number
    msg: string
    data: T
  }

  // uni.uploadFile文件上传参数
  type IUniUploadFileOptions = {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  type IUserInfo = {
    phone?: string
    avatar?: string
  }

  // 文件预览相关类型
  type FileType = 'image' | 'video' | 'document' | 'richtext'

  type FileItem = {
    id?: string
    name: string
    url: string
    type: FileType
    size?: number
    extension?: string
    thumbnail?: string
  }

  type PreviewOptions = {
    current?: number
    files: FileItem[]
    showDownload?: boolean
    showShare?: boolean
  }

  type DocumentType = 'pdf' | 'doc' | 'docx' | 'xls' | 'xlsx' | 'ppt' | 'pptx'
}

export {} // 防止模块污染
