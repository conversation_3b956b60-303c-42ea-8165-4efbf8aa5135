{"globals": {"Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "PropType": true, "Ref": true, "VNode": true, "WritableComputedRef": true, "computed": true, "createApp": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "getCurrentInstance": true, "getCurrentScope": true, "h": true, "inject": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "markRaw": true, "nextTick": true, "onActivated": true, "onAddToFavorites": true, "onBackPress": true, "onBeforeMount": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onError": true, "onErrorCaptured": true, "onHide": true, "onLaunch": true, "onLoad": true, "onMounted": true, "onNavigationBarButtonTap": true, "onNavigationBarSearchInputChanged": true, "onNavigationBarSearchInputClicked": true, "onNavigationBarSearchInputConfirmed": true, "onNavigationBarSearchInputFocusChanged": true, "onPageNotFound": true, "onPageScroll": true, "onPullDownRefresh": true, "onReachBottom": true, "onReady": true, "onRenderTracked": true, "onRenderTriggered": true, "onResize": true, "onScopeDispose": true, "onServerPrefetch": true, "onShareAppMessage": true, "onShareTimeline": true, "onShow": true, "onTabItemTap": true, "onThemeChange": true, "onUnhandledRejection": true, "onUnload": true, "onUnmounted": true, "onUpdated": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "resolveComponent": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useRequest": true, "useSlots": true, "useUpload": true, "useUpload2": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true, "DirectiveBinding": true, "MaybeRef": true, "MaybeRefOrGetter": true, "onWatcherCleanup": true, "useId": true, "useModel": true, "useTemplateRef": true, "showToast": true, "getFileType": true, "previewDocument": true, "previewFile": true, "previewFiles": true, "previewImages": true, "previewRichText": true, "previewVideo": true, "useFilePreview": true, "useFileUpload": true}}