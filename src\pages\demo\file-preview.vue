<route lang="json5">
{
  style: {
    navigationBarTitleText: '文件预览演示',
  },
}
</route>

<template>
  <view class="demo-page">
    <view class="demo-section">
      <view class="section-title">图片预览演示</view>
      <view class="demo-content">
        <ImagePreview 
          :images="demoImages"
          :grid-cols="3"
          :max-display="6"
          :show-mask="true"
        />
      </view>
    </view>
    
    <view class="demo-section">
      <view class="section-title">视频预览演示</view>
      <view class="demo-content">
        <VideoPreview 
          :video-file="demoVideo"
          :show-info="true"
          :show-actions="true"
          :click-to-play="true"
        />
      </view>
    </view>
    
    <view class="demo-section">
      <view class="section-title">文档预览演示</view>
      <view class="demo-content">
        <view class="document-list">
          <view 
            v-for="(doc, index) in demoDocuments"
            :key="index"
            class="document-item"
            @click="previewDocument(doc)"
          >
            <view class="doc-icon">
              <i :class="getDocIcon(doc)" class="text-2xl"></i>
            </view>
            <view class="doc-info">
              <text class="doc-name">{{ doc.name }}</text>
              <text class="doc-type">{{ getDocTypeName(doc.extension) }}</text>
            </view>
            <view class="doc-action">
              <i class="i-carbon-chevron-right text-gray-400"></i>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="demo-section">
      <view class="section-title">富文本预览演示</view>
      <view class="demo-content">
        <RichTextPreview 
          :content="demoRichText"
          title="富文本内容演示"
          :use-rich-text="true"
          :show-copy-button="true"
          :show-share-button="true"
        />
      </view>
    </view>
    
    <view class="demo-section">
      <view class="section-title">文件上传演示</view>
      <view class="demo-content">
        <view class="upload-buttons">
          <wd-button type="primary" @click="chooseFileType">
            选择文件
          </wd-button>
          <wd-button type="default" @click="chooseImages">
            选择图片
          </wd-button>
          <wd-button type="default" @click="chooseVideos">
            选择视频
          </wd-button>
        </view>
        
        <!-- 上传结果展示 -->
        <view v-if="uploadedFiles.length > 0" class="upload-results">
          <view class="results-title">已上传文件：</view>
          <view 
            v-for="(file, index) in uploadedFiles"
            :key="index"
            class="result-item"
            @click="previewFile(file)"
          >
            <view class="result-icon">
              <i :class="getFileIcon(file)" class="text-lg"></i>
            </view>
            <view class="result-info">
              <text class="result-name">{{ file.name }}</text>
              <text class="result-type">{{ getFileTypeName(file.type) }}</text>
            </view>
            <wd-button 
              type="default" 
              size="small"
              @click.stop="removeFile(index)"
            >
              删除
            </wd-button>
          </view>
        </view>
        
        <!-- 加载状态 -->
        <view v-if="loading" class="upload-loading">
          <wd-loading type="circular" />
          <text class="loading-text">上传中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { 
  ImagePreview, 
  VideoPreview, 
  RichTextPreview,
  previewFile
} from '@/components/file-preview'
import { useFileUpload } from '@/hooks/useUpload'

// 演示数据
const demoImages: FileItem[] = [
  {
    id: '1',
    name: '演示图片1.jpg',
    url: 'https://picsum.photos/400/300?random=1',
    type: 'image',
    thumbnail: 'https://picsum.photos/200/150?random=1'
  },
  {
    id: '2',
    name: '演示图片2.jpg',
    url: 'https://picsum.photos/400/300?random=2',
    type: 'image',
    thumbnail: 'https://picsum.photos/200/150?random=2'
  },
  {
    id: '3',
    name: '演示图片3.jpg',
    url: 'https://picsum.photos/400/300?random=3',
    type: 'image',
    thumbnail: 'https://picsum.photos/200/150?random=3'
  },
  {
    id: '4',
    name: '演示图片4.jpg',
    url: 'https://picsum.photos/400/300?random=4',
    type: 'image',
    thumbnail: 'https://picsum.photos/200/150?random=4'
  }
]

const demoVideo: FileItem = {
  id: '1',
  name: '演示视频.mp4',
  url: 'https://example.com/demo-video.mp4',
  type: 'video',
  thumbnail: 'https://picsum.photos/400/225?random=5',
  size: 10485760
}

const demoDocuments: FileItem[] = [
  {
    id: '1',
    name: '演示文档.pdf',
    url: 'https://example.com/demo.pdf',
    type: 'document',
    extension: 'pdf',
    size: 2048000
  },
  {
    id: '2',
    name: '数据表格.xlsx',
    url: 'https://example.com/data.xlsx',
    type: 'document',
    extension: 'xlsx',
    size: 512000
  },
  {
    id: '3',
    name: '演示文稿.pptx',
    url: 'https://example.com/presentation.pptx',
    type: 'document',
    extension: 'pptx',
    size: 3072000
  }
]

const demoRichText = `
  <h2>富文本内容演示</h2>
  <p>这是一个富文本内容的演示，支持多种HTML标签的渲染。</p>
  
  <h3>功能特点：</h3>
  <ul>
    <li><strong>支持基本格式</strong>：粗体、斜体、下划线等</li>
    <li><em>支持列表</em>：有序列表和无序列表</li>
    <li>支持链接：<a href="https://example.com">点击访问</a></li>
    <li>支持图片显示和点击预览</li>
  </ul>
  
  <blockquote>
    <p>这是一个引用块，用于突出显示重要内容。</p>
  </blockquote>
  
  <p>还支持代码显示：<code>console.log('Hello World')</code></p>
  
  <pre>
// 代码块示例
function greet(name) {
  return \`Hello, \${name}!\`;
}
  </pre>
`

// 文件上传功能
const {
  loading,
  uploadedFiles,
  chooseImages,
  chooseVideos,
  chooseFileType,
  removeFile
} = useFileUpload({
  maxCount: 5,
  fileTypes: ['image', 'video', 'document'],
  maxSize: 10
})

// 预览文档
const previewDocument = (doc: FileItem) => {
  previewFile(doc)
}

// 获取文档图标
const getDocIcon = (doc: FileItem) => {
  const iconMap = {
    pdf: 'i-carbon-document-pdf text-red-500',
    doc: 'i-carbon-document text-blue-500',
    docx: 'i-carbon-document text-blue-500',
    xls: 'i-carbon-table text-green-500',
    xlsx: 'i-carbon-table text-green-500',
    ppt: 'i-carbon-presentation-file text-orange-500',
    pptx: 'i-carbon-presentation-file text-orange-500'
  }
  return iconMap[doc.extension] || 'i-carbon-document text-gray-500'
}

// 获取文档类型名称
const getDocTypeName = (extension: string) => {
  const nameMap = {
    pdf: 'PDF文档',
    doc: 'Word文档',
    docx: 'Word文档',
    xls: 'Excel表格',
    xlsx: 'Excel表格',
    ppt: 'PowerPoint演示文稿',
    pptx: 'PowerPoint演示文稿'
  }
  return nameMap[extension] || '未知文档'
}

// 获取文件图标
const getFileIcon = (file: FileItem) => {
  const iconMap = {
    image: 'i-carbon-image text-blue-500',
    video: 'i-carbon-video text-red-500',
    document: 'i-carbon-document text-green-500',
    richtext: 'i-carbon-text-creation text-purple-500'
  }
  return iconMap[file.type] || 'i-carbon-document text-gray-500'
}

// 获取文件类型名称
const getFileTypeName = (type: FileType) => {
  const nameMap = {
    image: '图片',
    video: '视频',
    document: '文档',
    richtext: '富文本'
  }
  return nameMap[type] || '未知'
}
</script>

<style scoped lang="scss">
.demo-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
  
  .demo-section {
    background: white;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    
    .section-title {
      background: #42b981;
      color: white;
      font-size: 32rpx;
      font-weight: bold;
      padding: 20rpx 30rpx;
    }
    
    .demo-content {
      padding: 30rpx;
    }
  }
  
  .document-list,
  .upload-results {
    .document-item,
    .result-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
      margin-bottom: 16rpx;
      transition: all 0.3s ease;
      
      &:active {
        background: #e9ecef;
        transform: scale(0.98);
      }
      
      .doc-icon,
      .result-icon {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }
      
      .doc-info,
      .result-info {
        flex: 1;
        
        .doc-name,
        .result-name {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 4rpx;
        }
        
        .doc-type,
        .result-type {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
  
  .upload-buttons {
    display: flex;
    gap: 20rpx;
    margin-bottom: 30rpx;
    flex-wrap: wrap;
  }
  
  .results-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .upload-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx;
    
    .loading-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
