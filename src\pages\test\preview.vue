<route lang="json5">
{
  style: {
    navigationBarTitleText: '预览测试',
  },
}
</route>

<template>
  <view class="test-page">
    <view class="test-section">
      <view class="section-title">图片预览测试</view>
      <view class="test-buttons">
        <wd-button type="primary" @click="testImagePreview">
          测试图片预览
        </wd-button>
        <wd-button type="default" @click="testMultipleImages">
          测试多图预览
        </wd-button>
      </view>
    </view>
    
    <view class="test-section">
      <view class="section-title">视频预览测试</view>
      <view class="test-buttons">
        <wd-button type="primary" @click="testVideoPreview">
          测试视频预览
        </wd-button>
      </view>
    </view>
    
    <view class="test-section">
      <view class="section-title">文档预览测试</view>
      <view class="test-buttons">
        <wd-button type="primary" @click="testPdfPreview">
          测试PDF预览
        </wd-button>
        <wd-button type="default" @click="testWordPreview">
          测试Word预览
        </wd-button>
        <wd-button type="default" @click="testExcelPreview">
          测试Excel预览
        </wd-button>
      </view>
    </view>
    
    <view class="test-section">
      <view class="section-title">富文本预览测试</view>
      <view class="test-buttons">
        <wd-button type="primary" @click="testRichTextPreview">
          测试富文本预览
        </wd-button>
      </view>
    </view>
    
    <view class="test-section">
      <view class="section-title">文件上传测试</view>
      <view class="test-buttons">
        <wd-button type="primary" @click="testFileUpload">
          选择文件上传
        </wd-button>
        <wd-button type="default" @click="testImageUpload">
          选择图片上传
        </wd-button>
      </view>
      
      <!-- 显示上传结果 -->
      <view v-if="uploadedFiles.length > 0" class="upload-results">
        <view class="results-title">上传结果：</view>
        <view 
          v-for="(file, index) in uploadedFiles"
          :key="index"
          class="result-item"
          @click="previewUploadedFile(file)"
        >
          <text class="file-name">{{ file.name }}</text>
          <text class="file-type">{{ file.type }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { previewFile, previewFiles, previewImages, previewRichText } from '@/hooks/useFilePreview'
import { useFileUpload } from '@/hooks/useUpload'

// 文件上传功能
const { uploadedFiles, chooseFileType, chooseImages } = useFileUpload({
  maxCount: 3,
  fileTypes: ['image', 'video', 'document'],
  maxSize: 10
})

// 测试图片预览
const testImagePreview = () => {
  const imageFile: FileItem = {
    id: '1',
    name: '测试图片.jpg',
    url: 'https://picsum.photos/800/600?random=1',
    type: 'image'
  }
  previewFile(imageFile)
}

// 测试多图预览
const testMultipleImages = () => {
  const images: FileItem[] = [
    {
      id: '1',
      name: '图片1.jpg',
      url: 'https://picsum.photos/800/600?random=1',
      type: 'image'
    },
    {
      id: '2',
      name: '图片2.jpg',
      url: 'https://picsum.photos/800/600?random=2',
      type: 'image'
    },
    {
      id: '3',
      name: '图片3.jpg',
      url: 'https://picsum.photos/800/600?random=3',
      type: 'image'
    }
  ]
  
  previewImages({
    files: images,
    current: 0
  })
}

// 测试视频预览
const testVideoPreview = () => {
  const videoFile: FileItem = {
    id: '1',
    name: '测试视频.mp4',
    url: 'https://example.com/test-video.mp4',
    type: 'video',
    size: 10485760
  }
  previewFile(videoFile)
}

// 测试PDF预览
const testPdfPreview = () => {
  const pdfFile: FileItem = {
    id: '1',
    name: '测试文档.pdf',
    url: 'https://example.com/test.pdf',
    type: 'document',
    extension: 'pdf',
    size: 2048000
  }
  previewFile(pdfFile)
}

// 测试Word预览
const testWordPreview = () => {
  const wordFile: FileItem = {
    id: '1',
    name: '测试文档.docx',
    url: 'https://example.com/test.docx',
    type: 'document',
    extension: 'docx',
    size: 1024000
  }
  previewFile(wordFile)
}

// 测试Excel预览
const testExcelPreview = () => {
  const excelFile: FileItem = {
    id: '1',
    name: '测试表格.xlsx',
    url: 'https://example.com/test.xlsx',
    type: 'document',
    extension: 'xlsx',
    size: 512000
  }
  previewFile(excelFile)
}

// 测试富文本预览
const testRichTextPreview = () => {
  const richTextContent = `
    <h2>富文本测试内容</h2>
    <p>这是一个<strong>富文本</strong>预览的测试页面。</p>
    <ul>
      <li>支持HTML标签渲染</li>
      <li>支持文本复制和分享</li>
      <li>支持图片点击预览</li>
    </ul>
    <blockquote>
      <p>这是一个引用块的示例。</p>
    </blockquote>
  `
  
  previewRichText(richTextContent, '富文本测试')
}

// 测试文件上传
const testFileUpload = () => {
  chooseFileType()
}

// 测试图片上传
const testImageUpload = () => {
  chooseImages()
}

// 预览上传的文件
const previewUploadedFile = (file: FileItem) => {
  previewFile(file)
}
</script>

<style scoped lang="scss">
.test-page {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
  
  .test-section {
    background: white;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 30rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .test-buttons {
      display: flex;
      gap: 20rpx;
      flex-wrap: wrap;
    }
  }
  
  .upload-results {
    margin-top: 30rpx;
    
    .results-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 15rpx;
    }
    
    .result-item {
      background: #f8f9fa;
      border-radius: 8rpx;
      padding: 20rpx;
      margin-bottom: 10rpx;
      
      .file-name {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 5rpx;
      }
      
      .file-type {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}
</style>
