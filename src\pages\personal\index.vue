<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人中心',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging
    ref="paging"
    :loading-more-enabled="false"
    @query="queryList"
    :auto="false"
    :fixed="true"
    auto-show-system-loading
  >
    <view class="min-h-screen bg-gray-100">
      <!-- User Profile Header -->
      <view class="relative px-3 pt-4 pb-16 mb-12 bg-[#42b981]">
        <view class="flex items-start" :style="{ paddingTop: safeAreaInsets.top + 30 + 'px' }">
          <view class="relative">
            <view
              class="w-16 h-16 rounded-full overflow-hidden bg-white ring-2 ring-white shadow-lg"
            >
              <image
                class="w-full h-full object-cover"
                :src="
                  userInfo.avatar ||
                  'https://registry.npmmirror.com/wot-design-uni-assets/*/files/redpanda.jpg'
                "
                mode="aspectFill"
              />
            </view>
          </view>
          <view class="flex-1 ml-4 mt-1">
            <view class="flex items-center">
              <text class="text-lg font-bold text-white">{{ userInfo.name }}</text>
              <text class="text-xs text-white/80 ml-2 bg-white/20 px-2 py-1 rounded-full">
                138****1234
              </text>
            </view>
            <view class="mt-1 text-sm text-white/80">ID: {{ userInfo.id }}</view>
          </view>
          <view class="ml-2 px-2 mt-2 rounded-full flex items-center" @click="handleScan">
            <i class="i-iconamoon-scanner-fill text-2xl text-white"></i>
          </view>
        </view>
      </view>

      <!-- Points Card -->
      <view class="mx-3 -mt-24 bg-white rounded-xl p-4 shadow-md relative z-10">
        <view class="flex items-center justify-between">
          <view class="flex-1 text-center">
            <view class="text-lg font-bold text-[#42b981]">{{ userInfo.totalPoints }}</view>
            <view class="text-xs text-gray-500 mt-1">积分总额</view>
          </view>
          <view class="w-[1px] h-10 bg-gray-100"></view>
          <view class="flex-1 text-center">
            <view class="text-lg font-bold text-[#4a9ff6]">+{{ userInfo.earnedPoints }}</view>
            <view class="text-xs text-gray-500 mt-1">获得积分</view>
          </view>
          <view class="w-[1px] h-10 bg-gray-100"></view>
          <view class="flex-1 text-center">
            <view class="text-lg font-bold text-[#4277e2]">-{{ userInfo.usedPoints }}</view>
            <view class="text-xs text-gray-500 mt-1">消耗积分</view>
          </view>
        </view>
      </view>

      <!-- Menu Groups -->
      <view class="mx-3 mt-4 space-y-4">
        <!-- Medical Records Group -->
        <!-- <view class="bg-white rounded-xl shadow-sm overflow-hidden">
        <view class="px-4 py-3 border-b border-gray-50">
          <text class="text-base font-bold text-gray-800">就医服务</text>
        </view>
        <view class="grid grid-cols-3 gap-px bg-gray-100">
          <view
            v-for="item in medicalMenuItems"
            :key="item.title"
            class="bg-white p-4 flex flex-col items-center active:bg-gray-50"
            @click="navigateTo(item.path)"
          >
            <view
              class="w-10 h-10 rounded-full bg-[#42b981]/10 flex items-center justify-center mb-2"
            >
              <i :class="item.icon" class="text-xl text-white"></i>
            </view>
            <text class="text-sm text-gray-700">{{ item.title }}</text>
          </view>
        </view>
      </view> -->

        <!-- Settings Group -->
        <view class="bg-white rounded-xl shadow-sm overflow-hidden">
          <!-- <view class="px-4 py-3 border-b border-gray-50">
          <text class="text-base font-bold text-gray-800">设置</text>
        </view> -->
          <view>
            <view
              v-for="item in settingsMenuItems"
              :key="item.title"
              class="flex items-center px-4 py-4 active:bg-gray-50"
              :class="{
                'border-b-(1px solid gray-100)':
                  item !== settingsMenuItems[settingsMenuItems.length - 1],
              }"
              @click="navigateTo(item.path)"
            >
              <view class="w-9 h-9 rounded-full bg-[#42b981]/10 flex items-center justify-center">
                <i :class="item.icon" class="text-base text-white"></i>
              </view>
              <text class="flex-1 ml-3 text-sm text-gray-800">{{ item.title }}</text>
              <i class="i-material-symbols-chevron-right text-xl text-gray-400"></i>
            </view>
          </view>
        </view>
      </view>

      <!-- Logout Button -->
      <view class="mx-3 mt-6 mb-8">
        <wd-button block type="primary" @click="handleLogout">退出登录</wd-button>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { useUserStore } from '@/store'

const userStore = useUserStore()
const { safeAreaInsets } = uni.getSystemInfoSync()

const paging = ref(null)
const queryList = async () => {
  console.log('queryList')
  paging.value.complete([{ id: 1 }])
}
const userInfo = reactive({
  name: '刘田',
  id: '2022062900023',
  avatar: '',
  totalPoints: 1235,
  earnedPoints: 1435,
  usedPoints: 200,
})

// 就医服务菜单
const medicalMenuItems = [
  {
    title: '就诊记录',
    icon: 'i-material-symbols-article-outline',
    path: '/pages/medical/records',
  },
  {
    title: '处方单',
    icon: 'i-material-symbols-assignment-outline',
    path: '/pages/medical/prescriptions',
  },
  {
    title: '病案记录',
    icon: 'i-material-symbols-folder-outline',
    path: '/pages/medical/case-records',
  },
  {
    title: '检查报表',
    icon: 'i-material-symbols-analytics-outline',
    path: '/pages/medical/reports',
  },
  {
    title: '在线咨询',
    icon: 'i-material-symbols-chat-outline',
    path: '/pages/consultation/online',
  },
  {
    title: '健康档案',
    icon: 'i-material-symbols-medical-information-outline',
    path: '/pages/medical/health-records',
  },
]

// 设置菜单
const settingsMenuItems = [
  {
    title: '个人信息',
    icon: 'i-material-symbols-person-outline',
    path: '/pages/profile/personal-info',
  },
  {
    title: '我的消息',
    icon: 'i-material-symbols-chat-bubble-outline',
    path: '/pages/message/index',
  },
  {
    title: '满意度',
    icon: 'i-material-symbols-sentiment-satisfied-outline',
    path: '/pages/satisfaction/index',
  },
  {
    title: '穿戴设备',
    icon: 'i-material-symbols-watch-outline-rounded',
    path: '/pages-sub/smartDevice/index',
  },
  {
    title: '在线客服',
    icon: 'i-material-symbols-chat-paste-go-sharp',
    path: '/pages/customer-service/online',
  },
  {
    title: '意见反馈',
    icon: 'i-material-symbols-feedback-outline',
    path: '/pages/feedback/index',
  },
]

const navigateTo = (path: string) => {
  uni.navigateTo({ url: path })
}

const handleEditAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 这里可以处理头像上传逻辑
      console.log(res.tempFilePaths[0])
    },
  })
}

const handleScan = () => {
  // 显示用户二维码名片
  uni.scanCode({
    scanType: ['qrCode'],
    success: (res) => {
      console.log(res.result)
    },
  })
}

const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
      }
    },
  })
}
</script>

<style scoped>
/* 所有样式已转换为 Tailwind 类名 */
</style>
