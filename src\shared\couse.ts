export interface Course {
    id: number;
    category_id: number;
    title: string;
    sort: number;
    status: number;
    create_time: number;
    update_time: number;
    type: CourseType;
    content: string;
    delete_flag: number;
    is_top: number;
    is_hot: number;
    is_off: number;
    category_name: string;
  }

export enum CourseType {
    IMAGE = 1,
    VIDEO = 2,
    DOCUMENT = 3,
    RICH_TEXT = 4,
 
}
export interface CourseCategory {
    id: number | string;
    name: string;
    sort: number;
    status: number;
    title: string;
    create_time: number;
    update_time: number;
}