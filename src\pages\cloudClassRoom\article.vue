<route lang="json5">
{
  style: {
    navigationBarTitleText: '云课堂',
  },
}
</route>
<template>
  <view class="article-page">
    <!-- 文章头部 -->
    <view class="article-header">
      <view class="article-title">{{ article.title || '文章标题' }}</view>
      <view class="article-meta">
        <text class="meta-item">{{ article.author || '作者' }}</text>
        <text class="meta-item">{{ formatDate(article.createTime) }}</text>
        <text class="meta-item">阅读 {{ article.readCount || 0 }} {{ article.type }}</text>
      </view>
    </view>

    <!-- 文章内容 -->
    <view class="article-content" v-if="!loading">
      <!-- 根据类型显示不同内容 -->

      <!-- 类型：文章 (article) 或默认 -->
      <template v-if="article.type === 'article' || !article.type">
        <!-- 富文本内容 -->
         {{ article.content }}
        <RichTextPreview
          v-if="article.content"
          :content="article.content"
          :title="article.title"
          :show-copy-button="false"
          :show-share-button="false"
        />
      </template>

      <!-- 类型：图片集 (gallery) -->
      <template v-if="article.type === 'gallery'">
        <view class="gallery-content">
          <view class="gallery-description" v-if="article.description">
            <text class="description-text">{{ article.description }}</text>
          </view>
        </view>
      </template>

      <!-- 类型：视频课程 (video) -->
      <template v-if="article.type === 'video'">
        <view class="video-course-content">
          <!-- 主视频 -->
          <view v-if="article.mainVideo" class="main-video">
            <VideoPreview
              :video-file="article.mainVideo"
              :show-info="true"
              :show-actions="true"
              :click-to-play="true"
            />
          </view>

          <!-- 课程描述 -->
          <view v-if="article.description" class="course-description">
            <view class="description-title">课程介绍</view>
            <text class="description-text">{{ article.description }}</text>
          </view>
        </view>
      </template>

      <!-- 类型：文档资料 (document) -->
      <template v-if="article.type === 'document'">
        <view class="document-content">
          <!-- 文档描述 -->
          <view v-if="article.description" class="document-description">
            <text class="description-text">{{ article.description }}</text>
          </view>
        </view>
      </template>

      <!-- 类型：混合内容 (mixed) -->
      <template v-if="article.type === 'mixed'">
        <!-- 富文本内容 -->
        <RichTextPreview
          v-if="article.content"
          :content="article.content"
          :title="article.title"
          :show-copy-button="false"
          :show-share-button="false"
        />
      </template>

    

     
    </view>

    <!-- 操作按钮 -->
    <!-- <view class="article-actions">
      <wd-button type="default" @click="shareArticle">
        <i class="i-carbon-share mr-2"></i>
        分享文章
      </wd-button>
      <wd-button type="primary" @click="collectArticle">
        <i class="i-carbon-bookmark mr-2"></i>
        {{ article.isCollected ? '已收藏' : '收藏' }}
      </wd-button>
    </view> -->

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <wd-loading  />
      <text class="loading-text">正在加载...</text>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ImagePreview, VideoPreview, RichTextPreview } from '@/components/file-preview'
import { previewFile } from '@/hooks/useFilePreview'

// 文章数据
const article = reactive({
  type: '',
  id: '',
  title: '云课堂学习资料',
  author: '医院专家',
  createTime: new Date().toISOString(),
  readCount: 128,
  isCollected: false,
  description: '这是一个云课堂学习资料的描述信息',
  content: `
    <h2>欢迎来到云课堂</h2>
    <p>这里是医院的在线学习平台，为您提供专业的医疗知识和健康指导。</p>
    <h3>学习内容包括：</h3>
    <ul>
      <li>疾病预防与健康管理</li>
      <li>常见疾病的诊断与治疗</li>
      <li>医疗器械使用指南</li>
      <li>急救知识与技能</li>
    </ul>
    <blockquote>
      <p>知识就是力量，健康就是财富。让我们一起学习，共同进步！</p>
    </blockquote>
  `,
  attachments: [
    {
      id: '1',
      name: '医疗指南.pdf',
      url: 'https://example.com/medical-guide.pdf',
      type: 'document' as FileType,
      extension: 'pdf',
      size: 2048000
    },
    {
      id: '2',
      name: '健康数据表.xlsx',
      url: 'https://example.com/health-data.xlsx',
      type: 'document' as FileType,
      extension: 'xlsx',
      size: 512000
    }
  ],
  images: [
    {
      id: '1',
      name: '医院环境1.jpg',
      url: 'https://picsum.photos/400/300?random=1',
      type: 'image' as FileType,
      thumbnail: 'https://picsum.photos/200/150?random=1'
    },
    {
      id: '2',
      name: '医院环境2.jpg',
      url: 'https://picsum.photos/400/300?random=2',
      type: 'image' as FileType,
      thumbnail: 'https://picsum.photos/200/150?random=2'
    },
    {
      id: '3',
      name: '医院环境3.jpg',
      url: 'https://picsum.photos/400/300?random=3',
      type: 'image' as FileType,
      thumbnail: 'https://picsum.photos/200/150?random=3'
    }
  ],
  videos: [
    {
      id: '1',
      name: '健康宣传片.mp4',
      url: 'https://example.com/health-video.mp4',
      type: 'video' as FileType,
      thumbnail: 'https://picsum.photos/400/225?random=4',
      size: 10485760,
      duration: 600 // 10分钟
    }
  ],
  mainVideo: {
    id: 'main1',
    name: '主要课程视频.mp4',
    url: 'https://example.com/main-video.mp4',
    type: 'video' as FileType,
    thumbnail: 'https://picsum.photos/800/450?random=10',
    size: 52428800,
    duration: 1800 // 30分钟
  }
})

const loading = ref(false)

// 页面加载
onLoad(({ id,type='article' }) => {
  if (id) {
    article.type = type
    article.id = id
    loadArticle(id)
  }
})

// 加载文章数据
const loadArticle = async (id: string) => {
  loading.value = true
  try {
    // 这里调用实际的API
    // const response = await cloudClassroomCourseDetail({ id })
    // Object.assign(article, response.data)

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    console.error('加载文章失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 预览附件
const previewAttachment = (file: FileItem) => {
  previewFile(file)
}

// 下载文件
const downloadFile = (file: FileItem) => {
  uni.showLoading({
    title: '下载中...'
  })

  uni.downloadFile({
    url: file.url,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
      }
    },
    fail: (err) => {
      uni.hideLoading()
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  })
}

// 分享文章
const shareArticle = () => {
  const shareContent = `${article.title}\n\n${article.content.replace(/<[^>]*>/g, '').slice(0, 100)}...`

  // #ifdef MP-WEIXIN
  uni.share({
    provider: 'weixin',
    type: 0,
    title: article.title,
    summary: shareContent,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  })
  // #endif

  // #ifndef MP-WEIXIN
  uni.setClipboardData({
    data: shareContent,
    success: () => {
      uni.showToast({
        title: '内容已复制到剪贴板',
        icon: 'success'
      })
    }
  })
  // #endif
}

// 收藏文章
const collectArticle = () => {
  article.isCollected = !article.isCollected

  uni.showToast({
    title: article.isCollected ? '收藏成功' : '取消收藏',
    icon: 'success'
  })

  // 这里调用收藏API
  // collectArticleAPI({ id: article.id, isCollected: article.isCollected })
}

// 获取文件图标
const getFileIcon = (file: FileItem) => {
  const iconMap = {
    image: 'i-carbon-image text-blue-500',
    video: 'i-carbon-video text-red-500',
    document: 'i-carbon-document text-green-500',
    richtext: 'i-carbon-text-creation text-purple-500'
  }

  if (file.extension) {
    const extIconMap = {
      pdf: 'i-carbon-document-pdf text-red-500',
      doc: 'i-carbon-document text-blue-500',
      docx: 'i-carbon-document text-blue-500',
      xls: 'i-carbon-table text-green-500',
      xlsx: 'i-carbon-table text-green-500',
      ppt: 'i-carbon-presentation-file text-orange-500',
      pptx: 'i-carbon-presentation-file text-orange-500'
    }
    return extIconMap[file.extension] || iconMap[file.type] || 'i-carbon-document text-gray-500'
  }

  return iconMap[file.type] || 'i-carbon-document text-gray-500'
}

// 获取文件类型名称
const getFileTypeName = (type: FileType) => {
  const nameMap = {
    image: '图片',
    video: '视频',
    document: '文档',
    richtext: '富文本'
  }
  return nameMap[type] || '未知'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 小于1小时显示分钟
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }

  // 小于24小时显示小时
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }

  // 小于7天显示天数
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }

  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 播放视频
const playVideo = (video: FileItem) => {
  previewFile(video)
}

// 格式化视频时长
const formatDuration = (seconds: number): string => {
  if (!seconds) return '00:00'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
}
</script>

<style scoped lang="scss">
.article-page {
  min-height: 100vh;
  background: #f8f9fa;

  .article-header {
    background: white;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .article-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      line-height: 1.4;
      margin-bottom: 20rpx;
    }

    .article-meta {
      display: flex;
      gap: 20rpx;

      .meta-item {
        font-size: 24rpx;
        color: #666;

        &:not(:last-child)::after {
          content: '·';
          margin-left: 20rpx;
          color: #ccc;
        }
      }
    }
  }

  .article-content {
    background: white;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .article-attachments,
    .article-images,
    .article-videos {
      margin-top: 40rpx;

      .attachments-title,
      .images-title,
      .videos-title {
        display: flex;
        align-items: center;
        gap: 10rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
        padding-bottom: 10rpx;
        border-bottom: 2rpx solid #eee;
      }
    }

    .attachments-list {
      .attachment-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        margin-bottom: 16rpx;
        transition: all 0.3s ease;

        &:active {
          background: #e9ecef;
          transform: scale(0.98);
        }

        .attachment-icon {
          width: 80rpx;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: white;
          border-radius: 12rpx;
          margin-right: 20rpx;
        }

        .attachment-info {
          flex: 1;

          .attachment-name {
            display: block;
            font-size: 30rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .attachment-meta {
            font-size: 24rpx;
            color: #666;
          }
        }

        .attachment-action {
          margin-left: 20rpx;
        }
      }
    }

    .videos-list {
      .video-item {
        margin-bottom: 30rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .article-actions {
    background: white;
    padding: 30rpx;
    display: flex;
    gap: 20rpx;
    justify-content: center;
    margin-bottom: 20rpx;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;

    .loading-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #666;
    }
  }

  // 图片集样式
  .gallery-content {
    .gallery-description {
      margin-bottom: 30rpx;

      .description-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }

  // 视频课程样式
  .video-course-content {
    .main-video {
      margin-bottom: 30rpx;
    }

    .course-description {
      margin-bottom: 30rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;

      .description-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 15rpx;
      }

      .description-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }

    .related-videos {
      .related-title {
        display: flex;
        align-items: center;
        gap: 10rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
        padding-bottom: 10rpx;
        border-bottom: 2rpx solid #eee;
      }

      .videos-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20rpx;

        .video-card {
          position: relative;
          background: #f8f9fa;
          border-radius: 8rpx;
          overflow: hidden;

          .video-thumbnail {
            width: 100%;
            height: 200rpx;
          }

          .video-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80rpx;
            height: 80rpx;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .video-info {
            padding: 15rpx;

            .video-title {
              display: block;
              font-size: 26rpx;
              color: #333;
              margin-bottom: 5rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .video-duration {
              font-size: 22rpx;
              color: #666;
            }
          }
        }
      }
    }
  }

  // 文档资料样式
  .document-content {
    .document-description {
      margin-bottom: 30rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;

      .description-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }

    .documents-list {
      .documents-title {
        display: flex;
        align-items: center;
        gap: 10rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
        padding-bottom: 10rpx;
        border-bottom: 2rpx solid #eee;
      }

      .document-card {
        display: flex;
        align-items: center;
        padding: 25rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
        transition: all 0.3s ease;

        &:active {
          background: #e9ecef;
          transform: scale(0.98);
        }

        .doc-icon-large {
          width: 100rpx;
          height: 100rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: white;
          border-radius: 12rpx;
          margin-right: 25rpx;
        }

        .doc-details {
          flex: 1;

          .doc-name {
            display: block;
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .doc-type {
            display: block;
            font-size: 26rpx;
            color: #666;
            margin-bottom: 5rpx;
          }

          .doc-size {
            font-size: 24rpx;
            color: #999;
          }
        }

        .doc-actions {
          display: flex;
          flex-direction: column;
          gap: 10rpx;
        }
      }
    }
  }
}
</style>
