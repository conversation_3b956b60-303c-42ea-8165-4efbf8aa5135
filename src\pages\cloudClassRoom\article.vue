<route lang="json5">
{
  style: {
    navigationBarTitleText: '云课堂',
  },
}
</route>
<template>
  <view>
    <view class="bg-white rounded-lg p-4">
      <view class="text-lg font-bold">{{ article.title }}</view>
    </view>
  </view>
</template>
<script setup lang="ts">
// import { cloudClassroomCourseDetail } from '@/service/app'
const article = reactive({ title: '' })
// const getArticle = (id) => {
//   cloudClassroomCourseDetail({
//     id,
//   })
// }
onLoad(({ id }) => {
  // getArticle(id)
})
</script>
<style scoped lang="scss"></style>
