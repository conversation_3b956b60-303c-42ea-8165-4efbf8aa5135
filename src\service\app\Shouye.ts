/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                          ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
// @ts-ignore
import { CustomRequestOptions } from "@/interceptors/request";
import request from "@/utils/request";

/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
// @ts-ignore
import {
  CommonSmsCodeSendSmsCodeCreateData,
  IndexCodeLoginCreateData,
  IndexCodeLoginCreatePayload,
  IndexIndexListData,
  IndexLoginCreateData,
  IndexLoginCreatePayload,
} from "./data-contracts";

// shouye 模块接口
/**
 * No description *
 * @tags 首页
 * @name IndexIndexList
 * @summary 初始用户数据
 * @request GET:/admin/index/index */
export async function indexIndexList({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<IndexIndexListData>("/admin/index/index", {
    method: "GET",
    ...(options || {}),
  });
}
/**
 * No description *
 * @tags 首页
 * @name IndexLoginCreate
 * @summary 登录
 * @request POST:/admin/index/login */
export async function indexLoginCreate({
  body,
  options,
}: {
  body: IndexLoginCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<IndexLoginCreateData>("/admin/index/login", {
    method: "POST",

    data: body,
    ...(options || {}),
  });
}
/**
 * No description *
 * @tags 首页
 * @name CommonSmsCodeSendSmsCodeCreate
 * @summary 发送短信验证码
 * @request POST:/admin/common.SmsCode/sendSmsCode */
export async function commonSmsCodeSendSmsCodeCreate({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<CommonSmsCodeSendSmsCodeCreateData>(
    "/admin/common.SmsCode/sendSmsCode",
    {
      method: "POST",
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 首页
 * @name IndexCodeLoginCreate
 * @summary 验证码登录
 * @request POST:/admin/index/codeLogin */
export async function indexCodeLoginCreate({
  body,
  options,
}: {
  body: IndexCodeLoginCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<IndexCodeLoginCreateData>("/admin/index/codeLogin", {
    method: "POST",

    data: body,
    ...(options || {}),
  });
}
