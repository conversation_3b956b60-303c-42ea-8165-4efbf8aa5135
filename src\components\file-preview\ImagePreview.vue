<template>
  <view class="image-preview">
    <view v-if="images.length === 1" class="single-image" @click="handlePreview(0)">
      <image 
        :src="images[0].thumbnail || images[0].url" 
        :mode="imageMode"
        class="preview-image"
        :style="{
          width: '700rpx',
          height: '400rpx',
          borderRadius: '8rpx',
          overflow: 'hidden'
        }"
        :class="imageClass"
        @error="handleImageError"
        @load="handleImageLoad"
      />
      <view v-if="showMask" class="image-mask">
        <view class="mask-content">
          <i class="i-carbon-view text-white text-xl"></i>
          <text class="mask-text">点击预览</text>
        </view>
      </view>
    </view>
    
    <view v-else-if="images.length > 1" class="multiple-images">
      <view 
        v-for="(image, index) in displayImages" 
        :key="image.id || index"
        class="image-item"
        @click="handlePreview(index)"
      >
        <image 
          :src="image.thumbnail || image.url" 
          mode="aspectFill"
          class="grid-image"
          @error="handleImageError"
        />
        <view v-if="showMask" class="image-mask">
          <i class="i-carbon-view text-white text-lg"></i>
        </view>
      </view>
      
      <!-- 显示更多图片的遮罩 -->
      <view 
        v-if="images.length > maxDisplay" 
        class="more-images"
        @click="handlePreview(maxDisplay - 1)"
      >
        <view class="more-mask">
          <text class="more-text">+{{ images.length - maxDisplay + 1 }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  images: FileItem[]
  imageMode?: 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix'
  maxDisplay?: number
  showMask?: boolean
  imageClass?: string
  gridCols?: number
}

const props = withDefaults(defineProps<Props>(), {
  imageMode: 'aspewidthFixctFill',
  maxDisplay: 9,
  showMask: true,
  imageClass: '',
  gridCols: 3
})

// 显示的图片列表（限制数量）
const displayImages = computed(() => {
  return props.images.slice(0, props.maxDisplay)
})

// 预览图片
const handlePreview = (index: number) => {
  const urls = props.images.map(img => img.url)
  
  uni.previewImage({
    urls,
    current: urls[index],
    longPressActions: {
      itemList: ['保存图片', '分享'],
      success: (data) => {
        if (data.tapIndex === 0) {
          // 保存图片
          saveImage(urls[data.index])
        } else if (data.tapIndex === 1) {
          // 分享图片
          shareImage(urls[data.index])
        }
      }
    }
  })
}

// 保存图片到相册
const saveImage = (url: string) => {
  uni.saveImageToPhotosAlbum({
    filePath: url,
    success: () => {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
    },
    fail: (err) => {
      console.error('保存图片失败:', err)
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  })
}

// 分享图片
const shareImage = (url: string) => {
  // #ifdef MP-WEIXIN
  uni.share({
    provider: 'weixin',
    type: 1,
    imageUrl: url,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: (err) => {
      console.error('分享失败:', err)
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  })
  // #endif
  
  // #ifndef MP-WEIXIN
  uni.showToast({
    title: '当前平台不支持分享',
    icon: 'none'
  })
  // #endif
}

// 图片加载错误处理
const handleImageError = (e: any) => {
  console.error('图片加载失败:', e)
}

// 图片加载成功处理
const handleImageLoad = (e: any) => {
  console.log('图片加载成功:', e)
}
</script>

<style scoped lang="scss">
.image-preview {
  .single-image {
    position: relative;
    display: inline-block;
    
    .preview-image {
      width: 100%;
      border-radius: 8rpx;
      overflow: hidden;
    }
  }
  
  .multiple-images {
    display: grid;
    grid-template-columns: repeat(v-bind(gridCols), 1fr);
    gap: 8rpx;
    
    .image-item {
      position: relative;
      aspect-ratio: 1;
      
      .grid-image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
        overflow: hidden;
      }
    }
    
    .more-images {
      position: relative;
      aspect-ratio: 1;
      
      .more-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .more-text {
          color: white;
          font-size: 32rpx;
          font-weight: bold;
        }
      }
    }
  }
  
  .image-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    
    .mask-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
      
      .mask-text {
        color: white;
        font-size: 24rpx;
      }
    }
  }
  
  .single-image:hover .image-mask,
  .image-item:hover .image-mask {
    opacity: 1;
  }
}
</style>
