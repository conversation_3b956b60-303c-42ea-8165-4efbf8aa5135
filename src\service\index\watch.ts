/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { http } from '@/utils/http'
export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}
export type RequestParams = any
import {
  SurveySurveyInfoCreateSurveyInfoCreateData,
  SurveySurveyInfoCreateSurveyInfoCreatePayload,
  SurveySurveyInfoGetSurveyInfoCreateData,
  SurveySurveyInfoGetSurveyInfoCreatePayload,
  SurveySurveyInfoGetSurveyViewCreateData,
  SurveySurveyInfoGetSurveyViewCreatePayload,
  SurveySurveyInfoUpdateSurveyInfoCreateData,
  SurveySurveyInfoUpdateSurveyInfoCreatePayload,
  SurveySurveyLoginGetHospitalListCreateData,
  SurveySurveyLoginGetSurgeryListCreateData,
  SurveySurveyLoginGetSurgeryListCreatePayload,
  SurveySurveyLoginHospitalLoginCreateData,
  SurveySurveyLoginHospitalLoginCreatePayload,
  SurveySurveyLoginUpdateHospitalPwdCreateData,
  SurveySurveyLoginUpdateHospitalPwdCreatePayload,
  SurveySurveyLoginUpdateSurgeryPwdCreateData,
  SurveySurveyLoginUpdateSurgeryPwdCreatePayload,
  SurveyWatchBindUserByAppCreateData,
  SurveyWatchBindUserByAppCreatePayload,
  SurveyWatchBindUserCreateData,
  SurveyWatchBindUserCreatePayload,
  SurveyWatchBindWacthCreateData,
  SurveyWatchBindWacthCreatePayload,
  SurveyWatchCreateUserCreateData,
  SurveyWatchCreateUserCreatePayload,
  SurveyWatchDelWacthCreateData,
  SurveyWatchDelWacthCreatePayload,
  SurveyWatchGetEarlyHistoryInfoCreateData,
  SurveyWatchGetEarlyHistoryInfoCreatePayload,
  SurveyWatchGetEarlyInfoCreateData,
  SurveyWatchGetEarlyInfoCreatePayload,
  SurveyWatchGetUserAllInfoCreateData,
  SurveyWatchGetUserAllInfoCreatePayload,
  SurveyWatchGetUserHistoryInfoCreateData,
  SurveyWatchGetUserHistoryInfoCreatePayload,
  SurveyWatchGetUserInfoCreateData,
  SurveyWatchGetUserInfoCreatePayload,
  SurveyWatchGetUserListCreateData,
  SurveyWatchGetUserListCreatePayload,
  SurveyWatchReportDetailCreateData,
  SurveyWatchReportDetailCreatePayload,
  SurveyWatchReportListCreateData,
  SurveyWatchReportListCreatePayload,
  SurveyWatchUnbindUserCreateData,
  SurveyWatchUnbindUserCreatePayload,
  SurveyWatchUpdateFallDownCreateData,
  SurveyWatchUpdateFallDownCreatePayload,
  SurveyWatchUpdateWatchThresholdCreateData,
  SurveyWatchUpdateWatchThresholdCreatePayload,
  SurveyWatchWatchListCreateData,
  SurveyWatchWatchListCreatePayload,
} from './watchTypes'
class Watch {
  /**
   * No description
   *
   * @name SurveySurveyInfoGetSurveyViewCreate
   * @summary 问卷调查详情
   * @request POST:/admin/survey.SurveyInfo/getSurveyView
   */
  surveySurveyInfoGetSurveyViewCreate = (
    data: SurveySurveyInfoGetSurveyViewCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyInfoGetSurveyViewCreateData>({
      url: `/admin/survey.SurveyInfo/getSurveyView`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveySurveyInfoGetSurveyInfoCreate
   * @summary 问卷调查列表
   * @request POST:/admin/survey.SurveyInfo/getSurveyInfo
   */
  surveySurveyInfoGetSurveyInfoCreate = (
    data: SurveySurveyInfoGetSurveyInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyInfoGetSurveyInfoCreateData>({
      url: `/admin/survey.SurveyInfo/getSurveyInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveySurveyInfoCreateSurveyInfoCreate
   * @summary 问卷调查创建
   * @request POST:/admin/survey.SurveyInfo/createSurveyInfo
   */
  surveySurveyInfoCreateSurveyInfoCreate = (
    data: SurveySurveyInfoCreateSurveyInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyInfoCreateSurveyInfoCreateData>({
      url: `/admin/survey.SurveyInfo/createSurveyInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveySurveyLoginGetHospitalListCreate
   * @summary 问卷调查医院列表
   * @request POST:/admin/survey.SurveyLogin/getHospitalList
   */
  surveySurveyLoginGetHospitalListCreate = (params: RequestParams = {}) =>
    http<SurveySurveyLoginGetHospitalListCreateData>({
      url: `/admin/survey.SurveyLogin/getHospitalList`,
      method: 'POST',
    })
  /**
   * No description
   *
   * @name SurveySurveyLoginHospitalLoginCreate
   * @summary 问卷调查登录
   * @request POST:/admin/survey.SurveyLogin/hospitalLogin
   */
  surveySurveyLoginHospitalLoginCreate = (
    data: SurveySurveyLoginHospitalLoginCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyLoginHospitalLoginCreateData>({
      url: `/admin/survey.SurveyLogin/hospitalLogin`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveySurveyInfoUpdateSurveyInfoCreate
   * @summary 问卷调查编辑
   * @request POST:/admin/survey.SurveyInfo/updateSurveyInfo
   */
  surveySurveyInfoUpdateSurveyInfoCreate = (
    data: SurveySurveyInfoUpdateSurveyInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyInfoUpdateSurveyInfoCreateData>({
      url: `/admin/survey.SurveyInfo/updateSurveyInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveySurveyLoginGetSurgeryListCreate
   * @summary 问卷调查诊室列表
   * @request POST:/admin/survey.SurveyLogin/getSurgeryList
   */
  surveySurveyLoginGetSurgeryListCreate = (
    data: SurveySurveyLoginGetSurgeryListCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyLoginGetSurgeryListCreateData>({
      url: `/admin/survey.SurveyLogin/getSurgeryList`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveySurveyLoginUpdateHospitalPwdCreate
   * @summary 修改医院密码
   * @request POST:/admin/survey.SurveyLogin/updateHospitalPwd
   */
  surveySurveyLoginUpdateHospitalPwdCreate = (
    data: SurveySurveyLoginUpdateHospitalPwdCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyLoginUpdateHospitalPwdCreateData>({
      url: `/admin/survey.SurveyLogin/updateHospitalPwd`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveySurveyLoginUpdateSurgeryPwdCreate
   * @summary 修改诊室密码
   * @request POST:/admin/survey.SurveyLogin/updateSurgeryPwd
   */
  surveySurveyLoginUpdateSurgeryPwdCreate = (
    data: SurveySurveyLoginUpdateSurgeryPwdCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveySurveyLoginUpdateSurgeryPwdCreateData>({
      url: `/admin/survey.SurveyLogin/updateSurgeryPwd`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveyWatchWatchListCreate
   * @summary 智能设备--智能设备列表
   * @request POST:/admin/survey.Watch/watchList
   */
  surveyWatchWatchListCreate = (
    data: SurveyWatchWatchListCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchWatchListCreateData>({
      url: `/admin/survey.Watch/watchList`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchUpdateWatchThresholdCreate
   * @summary 智能设备--修改设备阈值
   * @request POST:/admin/survey.Watch/updateWatchThreshold
   */
  surveyWatchUpdateWatchThresholdCreate = (
    data: SurveyWatchUpdateWatchThresholdCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchUpdateWatchThresholdCreateData>({
      url: `/admin/survey.Watch/updateWatchThreshold`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveyWatchBindUserCreate
   * @summary 智能设备--绑定用户
   * @request POST:/admin/survey.Watch/bindUser
   */
  surveyWatchBindUserCreate = (
    data: SurveyWatchBindUserCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchBindUserCreateData>({
      url: `/admin/survey.Watch/bindUser`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchUnbindUserCreate
   * @summary 智能设备--解绑用户
   * @request POST:/admin/survey.Watch/unbindUser
   */
  surveyWatchUnbindUserCreate = (
    data: SurveyWatchUnbindUserCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchUnbindUserCreateData>({
      url: `/admin/survey.Watch/unbindUser`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchCreateUserCreate
   * @summary 智能设备--创建用户
   * @request POST:/admin/survey.Watch/createUser
   */
  surveyWatchCreateUserCreate = (
    data: SurveyWatchCreateUserCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchCreateUserCreateData>({
      url: `/admin/survey.Watch/createUser`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchGetUserListCreate
   * @summary 智能设备--查询用户列表
   * @request POST:/admin/survey.Watch/getUserList
   */
  surveyWatchGetUserListCreate = (
    data: SurveyWatchGetUserListCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchGetUserListCreateData>({
      url: `/admin/survey.Watch/getUserList`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchGetUserInfoCreate
   * @summary 智能设备--查询当前绑定的用户测量信息
   * @request POST:/admin/survey.Watch/getUserInfo
   */
  surveyWatchGetUserInfoCreate = (
    data: SurveyWatchGetUserInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchGetUserInfoCreateData>({
      url: `/admin/survey.Watch/getUserInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchGetEarlyInfoCreate
   * @summary 智能设备--查询预警信息
   * @request POST:/admin/survey.Watch/getEarlyInfo
   */
  surveyWatchGetEarlyInfoCreate = (
    data: SurveyWatchGetEarlyInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchGetEarlyInfoCreateData>({
      url: `/admin/survey.Watch/getEarlyInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchBindUserByAppCreate
   * @summary 人本位系统绑定用户
   * @request POST:/admin/survey.Watch/bindUserByApp
   */
  surveyWatchBindUserByAppCreate = (
    data: SurveyWatchBindUserByAppCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchBindUserByAppCreateData>({
      url: `/admin/survey.Watch/bindUserByApp`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchBindWacthCreate
   * @summary 设备绑定机构
   * @request POST:/admin/survey.Watch/bindWacth
   */
  surveyWatchBindWacthCreate = (
    data: SurveyWatchBindWacthCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchBindWacthCreateData>({
      url: `/admin/survey.Watch/bindWacth`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchUpdateFallDownCreate
   * @summary 删除跌倒提醒
   * @request POST:/admin/survey.Watch/updateFallDown
   */
  surveyWatchUpdateFallDownCreate = (
    data: SurveyWatchUpdateFallDownCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchUpdateFallDownCreateData>({
      url: `/admin/survey.Watch/updateFallDown`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchDelWacthCreate
   * @summary 删除设备
   * @request POST:/admin/survey.Watch/delWacth
   */
  surveyWatchDelWacthCreate = (
    data: SurveyWatchDelWacthCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchDelWacthCreateData>({
      url: `/admin/survey.Watch/delWacth`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchGetUserAllInfoCreate
   * @summary 查询用户历史数据
   * @request POST:/admin/survey.Watch/getUserAllInfo
   */
  surveyWatchGetUserAllInfoCreate = (
    data: SurveyWatchGetUserAllInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchGetUserAllInfoCreateData>({
      url: `/admin/survey.Watch/getUserAllInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchReportListCreate
   * @summary ai报告列表
   * @request POST:/admin/survey.Watch/reportList
   */
  surveyWatchReportListCreate = (
    data: SurveyWatchReportListCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchReportListCreateData>({
      url: `/admin/survey.Watch/reportList`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
    })
  /**
   * No description
   *
   * @name SurveyWatchReportDetailCreate
   * @summary 测量分析详情
   * @request POST:/admin/survey.Watch/reportDetail
   */
  surveyWatchReportDetailCreate = (
    data: SurveyWatchReportDetailCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchReportDetailCreateData>({
      url: `/admin/survey.Watch/reportDetail`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchGetUserHistoryInfoCreate
   * @summary 用户历史数据图表
   * @request POST:/admin/survey.Watch/getUserHistoryInfo
   */
  surveyWatchGetUserHistoryInfoCreate = (
    data: SurveyWatchGetUserHistoryInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchGetUserHistoryInfoCreateData>({
      url: `/admin/survey.Watch/getUserHistoryInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  /**
   * No description
   *
   * @name SurveyWatchGetEarlyHistoryInfoCreate
   * @summary 用户历史数据预警信息
   * @request POST:/admin/survey.Watch/getEarlyHistoryInfo
   */
  surveyWatchGetEarlyHistoryInfoCreate = (
    data: SurveyWatchGetEarlyHistoryInfoCreatePayload,
    params: RequestParams = {},
  ) =>
    http<SurveyWatchGetEarlyHistoryInfoCreateData>({
      url: `/admin/survey.Watch/getEarlyHistoryInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  getMonthInfo = (data: any, params: RequestParams = {}) =>
    http<SurveyWatchGetEarlyHistoryInfoCreateData>({
      url: `/admin/survey.Watch/getMonthInfo`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
  getWatchFlag = (data: any) =>
    http<SurveyWatchGetEarlyHistoryInfoCreateData>({
      url: `/admin/survey.Watch/getWatchFlag`,
      method: 'POST',
      data: data,
      type: ContentType.FormData,
      disableEncrypt: true,
    })
}
export const watchApi = new Watch()
