<route lang="json5">
{
  style: {
    navigationBarTitleText: '服务',
  },
}
</route>
<template>
  <view class="min-h-screen bg-[#f5f7fa] pb-5">
    <!-- Tab Navigation -->
    <wd-tabs
      v-model="activeTab"
      swipeable
      animated
      class="sticky top-0 z-10 shadow-sm"
      active-color="#42b981"
      line-width="40"
    >
      <wd-tab
        :title="item.text"
        :name="item.name + ''"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <!-- 治疗方案卡片 -->
        <wd-card>
          <view class="flex items-center my-4 py-4 gap-2">
            <wd-circle
              :percent="treatmentInfo.progress"
              :size="100"
              :stroke-width="28"
              color="#42b981"
              class="mr-4"
            >
              <view class="flex flex-col items-center justify-center w-full h-full">
                <text class="text-xs text-gray-500 mb-1">服务进度</text>
                <text class="text-xs font-bold text-[#42b981]">{{ treatmentInfo.progress }}%</text>
              </view>
            </wd-circle>
            <view class="flex-1 gap-2">
              <view class="flex items-center justify-between">
                <text class="font-bold text-gray-800 text-base">xx疾病治疗方案</text>
                <wd-icon name="info" size="20px" color="#42b981" />
              </view>
              <view class="mt-2 bg-gray-50 rounded-lg px-4 py-3 text-sm text-gray-600 space-y-2">
                <view class="flex justify-between">
                  <text class="text-gray-400">预计周期</text>
                  <text>4周</text>
                </view>
                <view class="flex justify-between">
                  <text class="text-gray-400">开始时间</text>
                  <text>{{ treatmentInfo.startDate }}</text>
                </view>
                <view class="flex justify-between">
                  <text class="text-gray-400">服务节点</text>
                  <text>{{ treatmentInfo.medicationCount }}</text>
                </view>
              </view>
            </view>
          </view>
        </wd-card>

        <!-- 治疗进度时间线 -->
        <wd-card>
          <view>
            <wd-collapse v-model="activePanels" accordion>
              <wd-collapse-item
                v-for="(stage, idx) in treatmentStages"
                :key="idx"
                :name="String(idx)"
                :class="[
                  'mb-3 last:mb-0 rounded-xl overflow-hidden',
                  idx === 1 ? 'bg-[#f0f7ff]' : 'bg-white',
                ]"
              >
                <template #title>
                  <view class="flex items-center py-1">
                    <view
                      class="w-8 h-8 flex items-center justify-center rounded-full mr-3 text-white font-bold"
                      :class="[
                        idx === 0 ? 'bg-[#42b981]' : idx === 1 ? 'bg-[#4a9ff6]' : 'bg-gray-400',
                      ]"
                    >
                      {{ idx + 1 }}
                    </view>
                    <view class="flex-1">
                      <view class="font-bold text-base text-gray-800">{{ stage.title }}</view>
                      <view class="text-xs text-gray-400 mt-1">开始时间：{{ stage.date }}</view>
                    </view>
                    <view
                      v-if="stage.status"
                      class="ml-2 px-3 py-1 rounded-full text-xs font-medium"
                      :class="{
                        'bg-green-100 text-green-600': stage.status === '已完成',
                        'bg-blue-100 text-blue-500': stage.status === '进行中',
                        'bg-gray-100 text-gray-400': stage.status === '未开始',
                      }"
                    >
                      {{ stage.status }}
                    </view>
                  </view>
                </template>
                <view v-if="stage.children && stage.children.length" class="pt-3 px-2">
                  <view
                    v-for="(item, cidx) in stage.children"
                    :key="cidx"
                    class="flex items-start mb-4 last:mb-0"
                  >
                    <view class="w-6 flex-shrink-0 flex flex-col items-center pt-1">
                      <view
                        v-if="item.status === '已完成'"
                        class="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center"
                      >
                        <wd-icon name="success" size="12px" color="#fff" />
                      </view>
                      <view
                        v-else
                        class="w-4 h-4 rounded-full"
                        :class="[
                          item.status === '进行中'
                            ? 'bg-blue-500'
                            : item.status === '即将开始'
                              ? 'bg-orange-500'
                              : 'bg-gray-300',
                        ]"
                      ></view>
                      <view
                        v-if="cidx !== stage.children.length - 1"
                        class="w-px flex-1 bg-gray-200 mt-2"
                      ></view>
                    </view>
                    <view class="ml-4 flex-1">
                      <view class="flex items-center">
                        <view class="font-medium text-gray-800">{{ item.title }}</view>
                        <view
                          v-if="item.status === '即将开始'"
                          class="ml-2 px-2 py-0.5 rounded-full bg-orange-500 text-white text-xs"
                        >
                          即将开始
                        </view>
                        <view
                          v-else-if="item.status === '进行中'"
                          class="ml-2 px-2 py-0.5 rounded-full bg-blue-500 text-white text-xs"
                        >
                          进行中
                        </view>
                      </view>
                      <view class="flex items-center mt-1 text-xs text-gray-400">
                        <span>{{ item.date }}</span>
                      </view>
                      <view
                        v-if="item.details && item.details.length"
                        class="bg-white bg-opacity-50 rounded-lg px-4 py-3 mt-2 text-sm text-gray-600 space-y-1.5"
                      >
                        <view v-for="(d, didx) in item.details" :key="didx">{{ d }}</view>
                      </view>
                    </view>
                  </view>
                </view>
              </wd-collapse-item>
            </wd-collapse>
          </view>
        </wd-card>
      </wd-tab>
    </wd-tabs>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'

const tabList = [
  { name: 'inProgress', text: '进行中' },
  { name: 'completed', text: '已完成' },
]
const activeTab = ref('inProgress')
const activePanels = ref(['0'])

const treatmentInfo = reactive({
  progress: 72,
  startDate: '2024.02.21',
  medicationCount: 6,
})

// 优化数据结构，支持子阶段与状态
const treatmentStages = reactive([
  {
    title: '治疗前准备',
    date: '2024.02.21',
    status: '已完成',
    children: [
      {
        title: 'xxx治疗',
        date: '2024.02.21',
        status: '已完成',
        details: ['xxxxxxxxxxxxxxxxxx'],
      },
    ],
  },
  {
    title: '治疗中跟进',
    date: '2024.02.23',
    status: '进行中',
    children: [
      {
        title: 'xxx治疗',
        date: '2024.02.23',
        status: '已完成',
        details: ['xxxxxxxxxxxxxxxxxx'],
      },
      {
        title: 'xxxx调整',
        date: '2024.03.05',
        status: '即将开始',
        details: ['xxxxx', 'xxxxx'],
      },
      {
        title: '系统治疗',
        date: '2024.03.08',
        status: '未开始',
        details: ['探诊记录出血指数', '刮治+激光杀菌'],
      },
    ],
  },
  {
    title: '治疗后服务',
    date: '2024.04.10',
    status: '未开始',
    children: [],
  },
])
</script>

<style scoped>
:deep(.wd-tabs) {
  background-color: transparent !important;
}
:deep(.wd-tabs__line) {
  background-color: #42b981 !important;
}

:deep(.wd-collapse-item__header) {
  border: none !important;
}

:deep(.wd-collapse-item__wrapper) {
  border: none !important;
}
:deep(.wd-collapse-item__header) {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
</style>
