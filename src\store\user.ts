import { defineStore } from 'pinia'
import { ref } from 'vue'
import { indexCodeLoginCreate, indexLoginCreate } from '@/service/app'

const initState = { phone: '', avatar: '' }

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initState })

    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }
    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      userInfo.value = { ...initState }
    }
    const isLogined = computed(() => !!userInfo.value.token)

    // 登录函数
    const login = async (id: string, code: string, phone: string) => {
      try {
        const res = await indexCodeLoginCreate({
          body: {
            id,
            code,
            phone,
          },
        })

        if (res.code === 1) {
          setUserInfo({
            phone,
            avatar: '',
          })
          uni.setStorageSync('_phone', {
            value: phone,
            expires: new Date().getTime() + 1000 * 60 * 60 * 24 * 30,
          })
          return Promise.resolve(res)
        } else {
          return Promise.reject(res)
        }
      } catch (error) {
        return Promise.reject(error)
      }
    }

    // 登出函数
    const logout = () => {
      return new Promise<void>((resolve) => {
        // 清除用户信息
        clearUserInfo()
        // 清除本地存储
        uni.clearStorageSync()
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/index',
        })
        resolve()
      })
    }

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      isLogined,
      reset,
      login,
      logout,
    }
  },
  {
    persist: true,
  },
)
