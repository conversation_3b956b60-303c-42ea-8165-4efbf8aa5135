<template>
  <view class="rich-text-preview">
    <!-- 标题栏 -->
  
    <view v-if="title" class="title-bar">
      <text class="title-text">{{ title }}</text>
      <view class="title-actions">
        <wd-button 
          v-if="showCopyButton" 
          type="default" 
          size="small"
          @click="copyContent"
        >
          复制
        </wd-button>
        <wd-button 
          v-if="showShareButton" 
          type="primary" 
          size="small"
          @click="shareContent"
        >
          分享
        </wd-button>
      </view>
    </view>
    
    <!-- 富文本内容 -->
    <view class="content-container">
      <!-- 优先使用 rich-text 组件 -->
      <rich-text
        v-if="processedContent && processedContent.length > 0"
        :nodes="processedContent"
        class="rich-text-content"
        @tap="handleTap"
      />

      <!-- 备用：直接显示HTML内容 -->
      <view
        v-else-if="htmlContent"
        class="html-content"
        v-html="processedHtmlContent"
      />

      <!-- 最后备用：纯文本显示 -->
      <view v-else-if="props.content" class="plain-text-content">
        <text class="plain-text">{{ plainTextContent }}</text>
      </view>

      <!-- 空内容提示 -->
      <view v-else class="empty-content">
        <text class="empty-text">暂无内容</text>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <wd-loading />
      <text class="loading-text">正在加载内容...</text>
    </view>
    
    <!-- 错误状态 -->
    <view v-if="error" class="error-container">
      <i class="i-carbon-warning text-4xl text-red-500"></i>
      <text class="error-text">内容加载失败</text>
      <wd-button type="primary" size="small" @click="retry">
        重试
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  content?: string
  url?: string
  title?: string
  useRichText?: boolean
  showCopyButton?: boolean
  showShareButton?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  useRichText: true,
  showCopyButton: true,
  showShareButton: true,
  maxLength: 0
})

const loading = ref(false)
const error = ref(false)
const htmlContent = ref('')
const processedContent = ref<any>(null)

// 纯文本内容
const plainTextContent = computed(() => {
  if (props.content) {
    // 移除 HTML 标签，获取纯文本
    const text = props.content.replace(/<[^>]*>/g, '')
    return props.maxLength > 0 ? text.slice(0, props.maxLength) : text
  }
  return ''
})

// 处理后的HTML内容
const processedHtmlContent = computed(() => {
  if (!htmlContent.value) return ''

  // 简单的HTML处理，确保安全性
  return htmlContent.value
    .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除script标签
    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // 移除iframe标签
    .replace(/on\w+="[^"]*"/gi, '') // 移除事件处理器
})

// 初始化内容
onMounted(() => {
  initContent()
})

// 监听内容变化
watch(() => [props.content, props.url], () => {
  initContent()
})

// 初始化内容
const initContent = async () => {
  if (props.content) {
    processContent(props.content)
  } else if (props.url) {
    await loadContentFromUrl()
  }
}

// 处理内容
const processContent = (content: string) => {
  htmlContent.value = content

  if (props.useRichText) {
    try {
      // 将 HTML 转换为 rich-text 支持的格式
      processedContent.value = parseHtmlToNodes(content)
    } catch (err) {
      console.error('HTML 解析失败:', err)
      // 如果解析失败，直接使用HTML内容
      htmlContent.value = content
      error.value = false
    }
  }
}

// 从 URL 加载内容
const loadContentFromUrl = async () => {
  if (!props.url) return
  
  loading.value = true
  error.value = false
  
  try {
    const response = await uni.request({
      url: props.url,
      method: 'GET'
    })
    
    if (response.statusCode === 200) {
      processContent(response.data as string)
    } else {
      throw new Error('请求失败')
    }
  } catch (err) {
    console.error('加载内容失败:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

// 简单的 HTML 解析器（用于 rich-text）
const parseHtmlToNodes = (html: string) => {
  if (!html) return []

  try {
    // 简单处理HTML内容，转换为rich-text支持的格式
    const cleanHtml = html
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除script
      .replace(/<style[^>]*>.*?<\/style>/gi, '') // 移除style
      .replace(/on\w+="[^"]*"/gi, '') // 移除事件处理器

    // 如果内容很简单，直接返回文本节点
    if (!cleanHtml.includes('<')) {
      return [{
        type: 'text',
        text: cleanHtml
      }]
    }

    // 对于复杂HTML，返回一个包含HTML的节点
    return [{
      name: 'div',
      attrs: {
        style: 'line-height: 1.6; font-size: 28rpx; color: #333;'
      },
      children: [{
        type: 'text',
        text: cleanHtml.replace(/<[^>]*>/g, '') // 移除所有HTML标签，只保留文本
      }]
    }]
  } catch (err) {
    console.error('HTML解析失败:', err)
    // 解析失败时返回纯文本
    return [{
      type: 'text',
      text: html.replace(/<[^>]*>/g, '')
    }]
  }
}

// 处理点击事件
const handleTap = (e: any) => {
  console.log('富文本点击:', e)
}

// 处理链接点击
const handleLinkTap = (e: any) => {
  const { href } = e.detail
  
  uni.showModal({
    title: '打开链接',
    content: `是否要打开链接：${href}`,
    success: (res) => {
      if (res.confirm) {
        // #ifdef H5
        window.open(href, '_blank')
        // #endif
        
        // #ifdef MP-WEIXIN
        uni.setClipboardData({
          data: href,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            })
          }
        })
        // #endif
      }
    }
  })
}

// 处理图片点击
const handleImgTap = (e: any) => {
  const { src } = e.detail
  
  uni.previewImage({
    urls: [src],
    current: src
  })
}

// 处理错误
const handleError = (e: any) => {
  console.error('富文本渲染错误:', e)
}

// 复制内容
const copyContent = () => {
  const textContent = plainTextContent.value
  
  uni.setClipboardData({
    data: textContent,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  })
}

// 分享内容
const shareContent = () => {
  const textContent = plainTextContent.value
  const shareTitle = props.title || '分享内容'
  
  // #ifdef MP-WEIXIN
  uni.share({
    provider: 'weixin',
    type: 0,
    title: shareTitle,
    summary: textContent.slice(0, 100),
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  })
  // #endif
  
  // #ifndef MP-WEIXIN
  uni.setClipboardData({
    data: `${shareTitle}\n\n${textContent}`,
    success: () => {
      uni.showToast({
        title: '内容已复制到剪贴板',
        icon: 'success'
      })
    }
  })
  // #endif
}

// 重试加载
const retry = () => {
  error.value = false
  initContent()
}
</script>

<style scoped lang="scss">
.rich-text-preview {
  .title-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    
    .title-text {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-right: 20rpx;
    }
    
    .title-actions {
      display: flex;
      gap: 10rpx;
    }
  }
  
  .content-container {
    .rich-text-content,
    .html-content {
      line-height: 1.6;
      font-size: 28rpx;
      color: #333;

      // 为HTML内容添加基本样式
      :deep(h1), :deep(h2), :deep(h3) {
        font-weight: bold;
        margin: 20rpx 0 15rpx 0;
        color: #333;
      }

      :deep(h1) { font-size: 36rpx; }
      :deep(h2) { font-size: 32rpx; }
      :deep(h3) { font-size: 30rpx; }

      :deep(p) {
        margin: 15rpx 0;
        line-height: 1.6;
      }

      :deep(ul), :deep(ol) {
        margin: 15rpx 0;
        padding-left: 30rpx;
      }

      :deep(li) {
        margin: 8rpx 0;
        line-height: 1.5;
      }

      :deep(blockquote) {
        border-left: 4rpx solid #42b981;
        padding-left: 20rpx;
        margin: 20rpx 0;
        background: #f8f9fa;
        padding: 15rpx 20rpx;
        border-radius: 4rpx;
        color: #666;
      }

      :deep(strong), :deep(b) {
        font-weight: bold;
        color: #333;
      }

      :deep(em), :deep(i) {
        font-style: italic;
      }
    }

    .plain-text-content {
      .plain-text {
        line-height: 1.6;
        font-size: 28rpx;
        color: #333;
      }
    }

    .empty-content {
      text-align: center;
      padding: 60rpx 20rpx;

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
  
  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
    
    .loading-text,
    .error-text {
      margin: 20rpx 0;
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
