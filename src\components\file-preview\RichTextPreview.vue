<template>
  <view class="rich-text-preview">
    <!-- 标题栏 -->
  
    <view v-if="title" class="title-bar">
      <text class="title-text">{{ title }}</text>
      <view class="title-actions">
        <wd-button 
          v-if="showCopyButton" 
          type="default" 
          size="small"
          @click="copyContent"
        >
          复制
        </wd-button>
        <wd-button 
          v-if="showShareButton" 
          type="primary" 
          size="small"
          @click="shareContent"
        >
          分享
        </wd-button>
      </view>
    </view>
    
    <!-- 富文本内容 -->
    <view class="content-container">
      <!-- 使用 rich-text 组件 -->
      <rich-text 
        v-if="useRichText && processedContent"
        :nodes="processedContent"
        class="rich-text-content"
        @tap="handleTap"
      />
      
      <!-- 使用简化的HTML渲染 -->
      <view
        v-else-if="htmlContent"
        class="html-content"
        v-html="processedHtmlContent"
      />
      
      <!-- 纯文本显示 -->
      <view v-else class="plain-text-content">
        <text class="plain-text">{{ plainTextContent }}</text>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <wd-loading type="circular" />
      <text class="loading-text">正在加载内容...</text>
    </view>
    
    <!-- 错误状态 -->
    <view v-if="error" class="error-container">
      <i class="i-carbon-warning text-4xl text-red-500"></i>
      <text class="error-text">内容加载失败</text>
      <wd-button type="primary" size="small" @click="retry">
        重试
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  content?: string
  url?: string
  title?: string
  useRichText?: boolean
  showCopyButton?: boolean
  showShareButton?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  useRichText: true,
  showCopyButton: true,
  showShareButton: true,
  maxLength: 0
})

const loading = ref(false)
const error = ref(false)
const htmlContent = ref('')
const processedContent = ref<any>(null)

// 纯文本内容
const plainTextContent = computed(() => {
  if (props.content) {
    // 移除 HTML 标签，获取纯文本
    const text = props.content.replace(/<[^>]*>/g, '')
    return props.maxLength > 0 ? text.slice(0, props.maxLength) : text
  }
  return ''
})

// 处理后的HTML内容
const processedHtmlContent = computed(() => {
  if (!htmlContent.value) return ''

  // 简单的HTML处理，确保安全性
  return htmlContent.value
    .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除script标签
    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // 移除iframe标签
    .replace(/on\w+="[^"]*"/gi, '') // 移除事件处理器
})

// 初始化内容
onMounted(() => {
  initContent()
})

// 监听内容变化
watch(() => [props.content, props.url], () => {
  initContent()
})

// 初始化内容
const initContent = async () => {
  if (props.content) {
    processContent(props.content)
  } else if (props.url) {
    await loadContentFromUrl()
  }
}

// 处理内容
const processContent = (content: string) => {
  htmlContent.value = content
  
  if (props.useRichText) {
    try {
      // 将 HTML 转换为 rich-text 支持的格式
      processedContent.value = parseHtmlToNodes(content)
    } catch (err) {
      console.error('HTML 解析失败:', err)
      error.value = true
    }
  }
}

// 从 URL 加载内容
const loadContentFromUrl = async () => {
  if (!props.url) return
  
  loading.value = true
  error.value = false
  
  try {
    const response = await uni.request({
      url: props.url,
      method: 'GET'
    })
    
    if (response.statusCode === 200) {
      processContent(response.data as string)
    } else {
      throw new Error('请求失败')
    }
  } catch (err) {
    console.error('加载内容失败:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

// 简单的 HTML 解析器（用于 rich-text）
const parseHtmlToNodes = (html: string) => {
  // 这里实现一个简单的 HTML 解析器
  // 实际项目中建议使用专业的 HTML 解析库
  const nodes: any[] = []
  
  // 简单处理一些常见标签
  let processedHtml = html
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '\n\n')
    .replace(/<br\s*\/?>/g, '\n')
    .replace(/<strong>(.*?)<\/strong>/g, '$1')
    .replace(/<b>(.*?)<\/b>/g, '$1')
    .replace(/<em>(.*?)<\/em>/g, '$1')
    .replace(/<i>(.*?)<\/i>/g, '$1')
  
  // 移除其他 HTML 标签
  processedHtml = processedHtml.replace(/<[^>]*>/g, '')
  
  nodes.push({
    name: 'div',
    attrs: {
      style: 'line-height: 1.6; font-size: 28rpx;'
    },
    children: [{
      type: 'text',
      text: processedHtml
    }]
  })
  
  return nodes
}

// 处理点击事件
const handleTap = (e: any) => {
  console.log('富文本点击:', e)
}

// 处理链接点击
const handleLinkTap = (e: any) => {
  const { href } = e.detail
  
  uni.showModal({
    title: '打开链接',
    content: `是否要打开链接：${href}`,
    success: (res) => {
      if (res.confirm) {
        // #ifdef H5
        window.open(href, '_blank')
        // #endif
        
        // #ifdef MP-WEIXIN
        uni.setClipboardData({
          data: href,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            })
          }
        })
        // #endif
      }
    }
  })
}

// 处理图片点击
const handleImgTap = (e: any) => {
  const { src } = e.detail
  
  uni.previewImage({
    urls: [src],
    current: src
  })
}

// 处理错误
const handleError = (e: any) => {
  console.error('富文本渲染错误:', e)
}

// 复制内容
const copyContent = () => {
  const textContent = plainTextContent.value
  
  uni.setClipboardData({
    data: textContent,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  })
}

// 分享内容
const shareContent = () => {
  const textContent = plainTextContent.value
  const shareTitle = props.title || '分享内容'
  
  // #ifdef MP-WEIXIN
  uni.share({
    provider: 'weixin',
    type: 0,
    title: shareTitle,
    summary: textContent.slice(0, 100),
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  })
  // #endif
  
  // #ifndef MP-WEIXIN
  uni.setClipboardData({
    data: `${shareTitle}\n\n${textContent}`,
    success: () => {
      uni.showToast({
        title: '内容已复制到剪贴板',
        icon: 'success'
      })
    }
  })
  // #endif
}

// 重试加载
const retry = () => {
  error.value = false
  initContent()
}
</script>

<style scoped lang="scss">
.rich-text-preview {
  .title-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    
    .title-text {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-right: 20rpx;
    }
    
    .title-actions {
      display: flex;
      gap: 10rpx;
    }
  }
  
  .content-container {
    .rich-text-content,
    .mp-html-content {
      line-height: 1.6;
      font-size: 28rpx;
      color: #333;
    }
    
    .plain-text-content {
      .plain-text {
        line-height: 1.6;
        font-size: 28rpx;
        color: #333;
      }
    }
  }
  
  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
    
    .loading-text,
    .error-text {
      margin: 20rpx 0;
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
