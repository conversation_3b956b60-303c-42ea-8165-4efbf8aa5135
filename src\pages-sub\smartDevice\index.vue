<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '分包页面 标题' },
}
</route>

<template>
  <z-paging
    ref="paging"
    :loading-more-enabled="false"
    @query="queryList"
    :auto="false"
    :fixed="true"
    auto-show-system-loading
    :style="'top:80rpx'"
  >
    <view class="grid grid-cols-2 gap-3 px-3 mt-3">
      <!-- Heart rate -->
      <HealthCard @click="goDetail(HealthType.HEART_RATE)">
        <template #icon>
          <view class="icon-bg icon-bg--heart">
            <i class="i-fa6-solid-heart-pulse text-white text-xs"></i>
          </view>
        </template>
        <template #title>心率</template>
        <template #value>
          {{ healthData.heartRate.real || '--' }}
          <text class="unit">次/分钟</text>
        </template>
        <template #actions>{{ formateDate(healthData.heartRate.time) || '--' }}</template>
        <template #content>
          <JnChart canvasId="heartRateRef" :option="heartRateOptions" lazyLoad />
        </template>
      </HealthCard>

      <!-- Blood oxygen -->
      <HealthCard @click="goDetail(HealthType.BLOOD_OXYGEN)">
        <template #icon>
          <view class="icon-bg icon-bg--blood-oxygen">
            <img :src="getCommonPath('bloodoxygen.svg')" alt="" class="w-full h-full" />
          </view>
        </template>
        <template #title>血氧</template>
        <template #value>
          {{ healthData.bloodOxygen.real || '--' }}
          <text class="unit">%</text>
        </template>
        <template #actions>{{ formateDate(healthData.bloodOxygen.time) || '--' }}</template>
        <template #content>
          <JnChart canvasId="bloodOxygenRef" :option="bloodOxygenOptions" lazyLoad />
        </template>
      </HealthCard>

      <!-- Temperature -->
      <HealthCard @click="goDetail(HealthType.TEMPERATURE)">
        <template #icon>
          <view class="icon-bg icon-bg--temperature">
            <i class="i-carbon-temperature-celsius text-white text-xs"></i>
          </view>
        </template>
        <template #title>体温</template>
        <template #value>
          {{ healthData.temperature.real || '--' }}
          <text class="unit">°C</text>
        </template>
        <template #actions>{{ formateDate(healthData.temperature.time) || '--' }}</template>
        <template #content>
          <JnChart canvasId="temperatureRef" :option="temperatureOptions" lazyLoad />
        </template>
      </HealthCard>

      <!-- Blood pressure -->
      <HealthCard @click="goDetail(HealthType.BLOOD_PRESSURE)">
        <template #icon>
          <view class="icon-bg icon-bg--blood-pressure">
            <img :src="getCommonPath('bloodPressure.png')" alt="" class="w-full h-full" />
          </view>
        </template>
        <template #title>血压</template>
        <template #value>
          {{ healthData.bloodPressure.sbp.real || '--' }}/
          <text>
            {{ healthData.bloodPressure.dbp.real || '--' }}
          </text>
          <text class="unit">mmHg</text>
        </template>
        <template #actions>
          {{ formateDate(healthData.bloodPressure.sbp.time) || '--' }}
        </template>
        <template #content>
          <JnChart canvasId="bloodPressureRef" :option="bloodPressureOptions" lazyLoad />
        </template>
      </HealthCard>

      <HealthCard class="col-span-2 pb-4" @click="goDetail(HealthType.VITAL_INDEX)">
        <template #icon>
          <view class="icon-bg icon-bg--vital">
            <img :src="getCommonPath('feel.svg')" alt="" class="w-full h-full" />
          </view>
        </template>
        <template #title>心理指标</template>

        <template #actions>
          {{ formateDate(healthData.psychology.depression.time) || '--' }}
        </template>
        <template #content>
          <view class="w-full h-[140%]">
            <JnChart canvasId="psychologyRef" :option="psychologyOptions" lazyLoad />
          </view>
        </template>
        <template #footer>
          <view class="flex items-center justify-between px-3 mt-8 text-gray-400 text-sm">
            <view>0-29 正常</view>
            <view>30-59 轻度</view>
            <view>60-79 中度</view>
            <view>80-100 重度</view>
          </view>
        </template>
      </HealthCard>

      <!-- Heart index -->
      <HealthCard class="col-span-2 pb-4" @click="goDetail(HealthType.HEART_INDEX)">
        <template #icon>
          <view class="icon-bg icon-bg--heart-index">
            <img :src="getCommonPath('feel.svg')" alt="" class="w-full h-full" />
          </view>
        </template>
        <template #title>生理指标</template>
        <template #actions>{{ formateDate(healthData.physiology.bv.time) || '--' }}</template>
        <template #content>
          <view class="w-full h-[140%]">
            <JnChart canvasId="physiologyRef" :option="physiologyOptions" lazyLoad />
          </view>
        </template>
        <template #footer>
          <view class="flex items-center justify-between px-3 mt-20 text-gray-400 text-sm">
            <view>0-29 正常</view>
            <view>30-59 轻度</view>
            <view>60-79 中度</view>
            <view>80-100 重度</view>
          </view>
        </template>
      </HealthCard>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import zPaging from '@/uni_modules/z-paging/components/z-paging/z-paging.vue'
import JnChart from '../components/jn-chart/index.vue'
import HealthCard from '../components/HealthCard.vue'
const chartRef = ref(null)
import { HealthType, formateDate } from './shared'
import { useHealthCharts } from './useCharts'
import { getCommonPath } from '@/utils'
const opt1 = ref({
  legend: {
    orient: 'vertical',
    left: '2%',
    top: 'center',
    itemWidth: 0,
    itemHeight: 0,
    itemGap: 15,
    selectedMode: true,
    data: [
      {
        name: '压力',
        icon: 'rect',
      },
      {
        name: '焦虑',
        icon: 'rect',
      },
      {
        name: '抑郁',
        icon: 'rect',
      },
    ],
    textStyle: {
      rich: {
        colorBlock: {
          width: 16,
          height: 14,
          borderRadius: 2,
        },
        nameTxt: {
          padding: [0, 5, 0, 5],
          color: '#666',
          fontSize: 14,
        },
        valueTxt: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          padding: [0, 0, 0, 10],
        },
      },
    },
  },
  series: [
    {
      name: '生理指标',
      type: 'pie',
      radius: [10, 72],
      center: ['75%', '50%'],
      roseType: 'area',
      clockwise: true,
      startAngle: 90,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 3,
      },
      label: {
        show: false,
      },
      data: [
        {
          value: '49',
          name: '压力',
          itemStyle: {
            color: '#78C06E',
          },
        },
        {
          value: '39',
          name: '焦虑',
          itemStyle: {
            color: '#409EFF',
          },
        },
        {
          value: '27',
          name: '抑郁',
          itemStyle: {
            color: '#38C1B4',
          },
        },
      ],
    },
  ],
})
const {
  heartRateOptions,
  bloodOxygenOptions,
  temperatureOptions,
  bloodPressureOptions,
  moodOptions,
  psychologyOptions,
  physiologyOptions,
  heartRateRef,
  bloodOxygenRef,
  temperatureRef,
  bloodPressureRef,
  moodRef,
  psychologyRef,
  physiologyRef,
  healthData,
  fetchRecentHealthData,
  initCharts,
} = useHealthCharts()

const goDetail = (type: HealthType) => {
  console.log(type)
  uni.navigateTo({
    url: `/pages-sub/smartDevice/detail?type=${type}`,
  })
}
const paging = ref(null)
const queryList = async () => {
  await fetchRecentHealthData()
  console.log(physiologyOptions.value, psychologyOptions.value)
  paging.value.complete([{ id: 1 }])
}
onReady(() => {
  fetchRecentHealthData()
})
</script>

<style>
page {
  background-color: #f2f2f2;
}
</style>
<style lang="scss" scoped>
.page-container {
  padding: 16px;
}

.health-overview {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-text {
  margin-top: 12px;
  font-size: 14px;
  color: #666;
}

.top-reminder {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #f56c6c;
  background-color: rgb(255 229 229 / 60%);
  border-radius: 8px;
}

.bind-btn {
  margin-left: 16px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.unit {
  font-size: 12px;
  color: #999;
}

.icon-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;

  &--heart {
    background-color: #f56c6c;
  }

  &--blood-oxygen {
    background-color: #409eff;
  }

  &--temperature {
    background-color: #67c23a;
  }

  &--blood-pressure {
    background-color: #e6a23c;
  }

  &--sleep {
    background-color: #f56c6c;
  }
}
</style>
