<template>
  <view class="consultation-container">
    <!-- Header -->
    <view class="consultation-header">
      <view class="header-title">在线问诊</view>
      <view class="header-subtitle">与医生实时沟通，获取专业建议</view>
    </view>

    <!-- Consultation Types -->
    <view class="consultation-types">
      <view class="type-item" @click="selectConsultationType('text')">
        <view class="type-icon">
          <wd-icon name="message-square" size="24px" color="#2196F3" />
        </view>
        <view class="type-text">
          <view class="type-title">文字问诊</view>
          <view class="type-desc">随时随地，方便快捷</view>
        </view>
      </view>

      <view class="type-item" @click="selectConsultationType('voice')">
        <view class="type-icon">
          <wd-icon name="phone" size="24px" color="#4CAF50" />
        </view>
        <view class="type-text">
          <view class="type-title">语音问诊</view>
          <view class="type-desc">清晰表达，高效沟通</view>
        </view>
      </view>

      <view class="type-item" @click="selectConsultationType('video')">
        <view class="type-icon">
          <wd-icon name="video" size="24px" color="#FF9800" />
        </view>
        <view class="type-text">
          <view class="type-title">视频问诊</view>
          <view class="type-desc">面对面交流，更加直观</view>
        </view>
      </view>

      <view class="type-item" @click="selectConsultationType('ai')">
        <view class="type-icon">
          <wd-icon name="cpu" size="24px" color="#9C27B0" />
        </view>
        <view class="type-text">
          <view class="type-title">AI 智能问诊</view>
          <view class="type-desc">24小时在线，即问即答</view>
        </view>
        <view class="type-tag">推荐</view>
      </view>
    </view>

    <!-- Recent Doctors -->
    <view class="section-title">最近咨询医生</view>
    <view class="doctors-container">
      <scroll-view class="doctors-scroll" scroll-x>
        <view class="doctors-list">
          <view
            v-for="(doctor, index) in recentDoctors"
            :key="index"
            class="doctor-item"
            @click="startConsultation(doctor)"
          >
            <image class="doctor-avatar" :src="doctor.avatar" mode="aspectFill" />
            <view class="doctor-name">{{ doctor.name }}</view>
            <view class="doctor-specialty">{{ doctor.specialty }}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- Common Symptoms -->
    <view class="section-title">常见症状</view>
    <view class="symptoms-container">
      <view
        v-for="(symptom, index) in commonSymptoms"
        :key="index"
        class="symptom-item"
        @click="selectSymptom(symptom)"
      >
        <view class="symptom-icon" :style="{ backgroundColor: symptom.color }">
          <wd-icon :name="symptom.icon" size="20px" color="white" />
        </view>
        <view class="symptom-name">{{ symptom.name }}</view>
      </view>
    </view>

    <!-- Health Records -->
    <view class="section-title">
      健康档案
      <text class="section-subtitle">问诊前请完善您的健康档案</text>
    </view>
    <view class="health-records">
      <view class="record-item" @click="navigateTo('/pages/health/basic-info')">
        <view class="record-icon">
          <wd-icon name="user" size="20px" color="#2196F3" />
        </view>
        <view class="record-text">
          <view class="record-title">基本信息</view>
          <view class="record-desc">身高、体重、血型等</view>
        </view>
        <view class="record-status completed">
          <wd-icon name="check" size="16px" color="#4CAF50" />
        </view>
      </view>

      <view class="record-item" @click="navigateTo('/pages/health/medical-history')">
        <view class="record-icon">
          <wd-icon name="clipboard" size="20px" color="#FF9800" />
        </view>
        <view class="record-text">
          <view class="record-title">病史记录</view>
          <view class="record-desc">既往病史、家族病史</view>
        </view>
        <view class="record-status incomplete">
          <wd-icon name="alert-circle" size="16px" color="#F44336" />
        </view>
      </view>

      <view class="record-item" @click="navigateTo('/pages/health/allergies')">
        <view class="record-icon">
          <wd-icon name="alert-triangle" size="20px" color="#F44336" />
        </view>
        <view class="record-text">
          <view class="record-title">过敏史</view>
          <view class="record-desc">药物过敏、食物过敏</view>
        </view>
        <view class="record-status incomplete">
          <wd-icon name="alert-circle" size="16px" color="#F44336" />
        </view>
      </view>
    </view>

    <!-- Consultation History -->
    <view class="section-title">问诊记录</view>
    <view class="history-container">
      <view
        v-for="(record, index) in consultationHistory"
        :key="index"
        class="history-item"
        @click="viewConsultationRecord(record)"
      >
        <view class="history-header">
          <view class="history-doctor">
            <image class="history-avatar" :src="record.doctorAvatar" mode="aspectFill" />
            <view class="history-doctor-info">
              <view class="history-doctor-name">{{ record.doctorName }}</view>
              <view class="history-doctor-specialty">{{ record.doctorSpecialty }}</view>
            </view>
          </view>
          <view class="history-date">{{ record.date }}</view>
        </view>
        <view class="history-content">
          <view class="history-symptom">症状：{{ record.symptom }}</view>
          <view class="history-diagnosis">诊断：{{ record.diagnosis }}</view>
        </view>
        <view class="history-footer">
          <view class="history-type" :class="record.typeClass">
            <wd-icon :name="record.typeIcon" size="14px" color="white" />
            <text class="type-text">{{ record.type }}</text>
          </view>
          <view class="history-action">
            <wd-button size="small" type="primary" plain>查看详情</wd-button>
          </view>
        </view>
      </view>

      <view v-if="consultationHistory.length === 0" class="empty-history">
        <wd-icon name="inbox" size="40px" color="#CCCCCC" />
        <view class="empty-text">暂无问诊记录</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

// Recent doctors
const recentDoctors = reactive([
  {
    id: 1,
    name: '张医生',
    specialty: '内科',
    avatar: '/static/doctor-1.jpg',
  },
  {
    id: 2,
    name: '李医生',
    specialty: '心血管科',
    avatar: '/static/doctor-2.jpg',
  },
  {
    id: 3,
    name: '王医生',
    specialty: '神经内科',
    avatar: '/static/doctor-3.jpg',
  },
  {
    id: 4,
    name: '赵医生',
    specialty: '消化内科',
    avatar: '/static/doctor-4.jpg',
  },
  {
    id: 5,
    name: '刘医生',
    specialty: '呼吸内科',
    avatar: '/static/doctor-5.jpg',
  },
])

// Common symptoms
const commonSymptoms = reactive([
  { id: 1, name: '头痛', icon: 'frown', color: '#F44336' },
  { id: 2, name: '发热', icon: 'thermometer', color: '#FF9800' },
  { id: 3, name: '咳嗽', icon: 'wind', color: '#2196F3' },
  { id: 4, name: '腹痛', icon: 'activity', color: '#4CAF50' },
  { id: 5, name: '过敏', icon: 'alert-triangle', color: '#9C27B0' },
  { id: 6, name: '失眠', icon: 'moon', color: '#607D8B' },
  { id: 7, name: '眩晕', icon: 'refresh-cw', color: '#00BCD4' },
  { id: 8, name: '其他', icon: 'more-horizontal', color: '#9E9E9E' },
])

// Consultation history
const consultationHistory = reactive([
  {
    id: 1,
    doctorName: '张医生',
    doctorSpecialty: '内科',
    doctorAvatar: '/static/doctor-1.jpg',
    date: '2024-03-15',
    symptom: '头痛、发热、乏力',
    diagnosis: '普通感冒',
    type: '视频问诊',
    typeIcon: 'video',
    typeClass: 'type-video',
  },
  {
    id: 2,
    doctorName: '李医生',
    doctorSpecialty: '心血管科',
    doctorAvatar: '/static/doctor-2.jpg',
    date: '2024-03-10',
    symptom: '胸闷、气短',
    diagnosis: '需进一步检查',
    type: '文字问诊',
    typeIcon: 'message-square',
    typeClass: 'type-text',
  },
])

// Methods
const selectConsultationType = (type: string) => {
  uni.navigateTo({
    url: `/pages/consultation/${type}`,
  })
}

const startConsultation = (doctor: any) => {
  uni.navigateTo({
    url: `/pages/consultation/chat?doctorId=${doctor.id}`,
  })
}

const selectSymptom = (symptom: any) => {
  uni.navigateTo({
    url: `/pages/consultation/symptom?id=${symptom.id}&name=${symptom.name}`,
  })
}

const navigateTo = (path: string) => {
  uni.navigateTo({ url: path })
}

const viewConsultationRecord = (record: any) => {
  uni.navigateTo({
    url: `/pages/consultation/record?id=${record.id}`,
  })
}
</script>

<style scoped>
.consultation-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20px;
}

.consultation-header {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  padding: 20px 15px;
  color: white;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.consultation-types {
  margin: 15px;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.type-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.type-item:last-child {
  border-bottom: none;
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.type-text {
  flex: 1;
}

.type-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.type-desc {
  font-size: 12px;
  color: #999;
  margin-top: 3px;
}

.type-tag {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: #ff5722;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 15px 15px 10px;
  display: flex;
  align-items: center;
}

.section-subtitle {
  font-size: 12px;
  color: #999;
  font-weight: normal;
  margin-left: 10px;
}

.doctors-container {
  margin: 0 15px;
}

.doctors-scroll {
  background-color: white;
  border-radius: 10px;
  padding: 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.doctors-list {
  display: flex;
  padding: 0 15px;
}

.doctor-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  width: 70px;
}

.doctor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f0f0;
  margin-bottom: 5px;
}

.doctor-name {
  font-size: 14px;
  color: #333;
  text-align: center;
}

.doctor-specialty {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.symptoms-container {
  margin: 0 15px;
  background-color: white;
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.symptom-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.symptom-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.symptom-name {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.health-records {
  margin: 0 15px;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.record-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.record-text {
  flex: 1;
}

.record-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.record-desc {
  font-size: 12px;
  color: #999;
  margin-top: 3px;
}

.record-status {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.completed {
  background-color: #e8f5e9;
}

.incomplete {
  background-color: #ffebee;
}

.history-container {
  margin: 0 15px;
}

.history-item {
  background-color: white;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.history-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.history-doctor {
  flex: 1;
  display: flex;
  align-items: center;
}

.history-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.history-doctor-info {
  margin-left: 10px;
}

.history-doctor-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.history-doctor-specialty {
  font-size: 12px;
  color: #999;
}

.history-date {
  font-size: 12px;
  color: #999;
}

.history-content {
  background-color: #f9f9f9;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
}

.history-symptom,
.history-diagnosis {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.history-diagnosis {
  margin-bottom: 0;
}

.history-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.history-type {
  display: flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 15px;
  font-size: 12px;
}

.type-text {
  color: white;
  margin-left: 5px;
}

.type-video {
  background-color: #ff9800;
}

.type-text {
  background-color: #2196f3;
}

.empty-history {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 10px;
}
</style>
