// TODO: 别忘加更改环境变量的 VITE_UPLOAD_BASEURL 地址。
import { getEnvBaseUploadUrl } from '@/utils'
import { getFileType } from './useFilePreview'

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`

/**
 * useUpload 是一个定制化的请求钩子，用于处理上传图片。
 * @param formData 额外传递给后台的数据，如{name: '菲鸽'}。
 * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。
 */
export default function useUpload<T = string>(formData: Record<string, any> = {}) {
  const loading = ref(false)
  const error = ref(false)
  const data = ref<T>()
  const run = () => {
    // #ifdef MP-WEIXIN
    // 微信小程序从基础库 2.21.0 开始， wx.chooseImage 停止维护，请使用 uni.chooseMedia 代替。
    // 微信小程序在2023年10月17日之后，使用本API需要配置隐私协议
    uni.chooseMedia({
      count: 1,
      mediaType: ['image'],
      success: (res) => {
        loading.value = true
        const tempFilePath = res.tempFiles[0].tempFilePath
        uploadFile<T>({ tempFilePath, formData, data, error, loading })
      },
      fail: (err) => {
        console.error('uni.chooseMedia err->', err)
        error.value = true
      },
    })
    // #endif
    // #ifndef MP-WEIXIN
    uni.chooseImage({
      count: 1,
      success: (res) => {
        loading.value = true
        const tempFilePath = res.tempFilePaths[0]
        uploadFile<T>({ tempFilePath, formData, data, error, loading })
      },
      fail: (err) => {
        console.error('uni.chooseImage err->', err)
        error.value = true
      },
    })
    // #endif
  }

  return { loading, error, data, run }
}

/**
 * useFileUpload - 支持多种文件类型的上传钩子
 * @param options 上传配置选项
 * @returns 返回上传相关的状态和方法
 */
export function useFileUpload<T = FileItem>(options: {
  formData?: Record<string, any>
  maxCount?: number
  fileTypes?: FileType[]
  maxSize?: number // 文件大小限制，单位：MB
} = {}) {
  const {
    formData = {},
    maxCount = 1,
    fileTypes = ['image', 'video', 'document'],
    maxSize = 10
  } = options

  const loading = ref(false)
  const error = ref(false)
  const uploadedFiles = ref<FileItem[]>([])

  // 选择并上传图片
  const chooseImages = () => {
    // #ifdef MP-WEIXIN
    uni.chooseMedia({
      count: maxCount,
      mediaType: ['image'],
      success: (res) => {
        handleSelectedFiles(res.tempFiles.map(file => ({
          tempFilePath: file.tempFilePath,
          size: file.size,
          type: 'image' as FileType
        })))
      },
      fail: handleChooseError
    })
    // #endif
    // #ifndef MP-WEIXIN
    uni.chooseImage({
      count: maxCount,
      success: (res) => {
        handleSelectedFiles(res.tempFilePaths.map(path => ({
          tempFilePath: path,
          type: 'image' as FileType
        })))
      },
      fail: handleChooseError
    })
    // #endif
  }

  // 选择并上传视频
  const chooseVideos = () => {
    // #ifdef MP-WEIXIN
    uni.chooseMedia({
      count: maxCount,
      mediaType: ['video'],
      success: (res) => {
        handleSelectedFiles(res.tempFiles.map(file => ({
          tempFilePath: file.tempFilePath,
          size: file.size,
          type: 'video' as FileType
        })))
      },
      fail: handleChooseError
    })
    // #endif
    // #ifndef MP-WEIXIN
    uni.chooseVideo({
      count: maxCount,
      success: (res) => {
        handleSelectedFiles([{
          tempFilePath: res.tempFilePath,
          size: res.size,
          type: 'video' as FileType
        }])
      },
      fail: handleChooseError
    })
    // #endif
  }

  // 选择并上传文档
  const chooseDocuments = () => {
    uni.chooseMessageFile({
      count: maxCount,
      type: 'file',
      success: (res) => {
        const files = res.tempFiles.map(file => ({
          tempFilePath: file.path,
          size: file.size,
          name: file.name,
          type: getFileType(file.name) as FileType
        }))
        handleSelectedFiles(files)
      },
      fail: handleChooseError
    })
  }

  // 处理选中的文件
  const handleSelectedFiles = (files: Array<{
    tempFilePath: string
    size?: number
    name?: string
    type: FileType
  }>) => {
    // 检查文件大小
    const oversizedFiles = files.filter(file =>
      file.size && file.size > maxSize * 1024 * 1024
    )

    if (oversizedFiles.length > 0) {
      uni.showToast({
        title: `文件大小不能超过${maxSize}MB`,
        icon: 'none'
      })
      return
    }

    // 批量上传文件
    uploadMultipleFiles(files)
  }

  // 批量上传文件
  const uploadMultipleFiles = async (files: Array<{
    tempFilePath: string
    size?: number
    name?: string
    type: FileType
  }>) => {
    loading.value = true
    error.value = false

    try {
      const uploadPromises = files.map(file => uploadSingleFile(file))
      const results = await Promise.all(uploadPromises)

      uploadedFiles.value = results.filter(Boolean) as FileItem[]

      uni.showToast({
        title: '上传成功',
        icon: 'success'
      })
    } catch (err) {
      console.error('文件上传失败:', err)
      error.value = true
      uni.showToast({
        title: '上传失败',
        icon: 'none'
      })
    } finally {
      loading.value = false
    }
  }

  // 上传单个文件
  const uploadSingleFile = (file: {
    tempFilePath: string
    size?: number
    name?: string
    type: FileType
  }): Promise<FileItem | null> => {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: VITE_UPLOAD_BASEURL,
        filePath: file.tempFilePath,
        name: 'file',
        formData: {
          ...formData,
          fileType: file.type
        },
        success: (uploadFileRes) => {
          try {
            const response = JSON.parse(uploadFileRes.data)
            if (response.code === 200) {
              const fileItem: FileItem = {
                id: response.data.id,
                name: file.name || response.data.name,
                url: response.data.url,
                type: file.type,
                size: file.size,
                extension: file.name?.split('.').pop()?.toLowerCase(),
                thumbnail: response.data.thumbnail
              }
              resolve(fileItem)
            } else {
              reject(new Error(response.msg || '上传失败'))
            }
          } catch (err) {
            reject(err)
          }
        },
        fail: reject
      })
    })
  }

  // 选择文件类型
  const chooseFileType = () => {
    const actions = []

    if (fileTypes.includes('image')) {
      actions.push('图片')
    }
    if (fileTypes.includes('video')) {
      actions.push('视频')
    }
    if (fileTypes.includes('document')) {
      actions.push('文档')
    }

    uni.showActionSheet({
      itemList: actions,
      success: (res) => {
        const selectedType = actions[res.tapIndex]
        switch (selectedType) {
          case '图片':
            chooseImages()
            break
          case '视频':
            chooseVideos()
            break
          case '文档':
            chooseDocuments()
            break
        }
      }
    })
  }

  // 处理选择错误
  const handleChooseError = (err: any) => {
    console.error('选择文件失败:', err)
    error.value = true
    uni.showToast({
      title: '选择文件失败',
      icon: 'none'
    })
  }

  // 清空已上传文件
  const clearFiles = () => {
    uploadedFiles.value = []
  }

  // 删除指定文件
  const removeFile = (index: number) => {
    uploadedFiles.value.splice(index, 1)
  }

  return {
    loading,
    error,
    uploadedFiles,
    chooseImages,
    chooseVideos,
    chooseDocuments,
    chooseFileType,
    clearFiles,
    removeFile
  }
}

function uploadFile<T>({ tempFilePath, formData, data, error, loading }) {
  uni.uploadFile({
    url: VITE_UPLOAD_BASEURL,
    filePath: tempFilePath,
    name: 'file',
    formData,
    success: (uploadFileRes) => {
      data.value = uploadFileRes.data as T
    },
    fail: (err) => {
      console.error('uni.uploadFile err->', err)
      error.value = true
    },
    complete: () => {
      loading.value = false
    },
  })
}
