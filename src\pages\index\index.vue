<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <view class="min-h-screen bg-gray-100">
    <!-- Hospital Header -->
    <view
      class="h-430rpx"
      style="background-image: linear-gradient(to bottom, #42b981 60%, #e8faf2 100%)"
    >
      <view
        class="flex items-center mb-2 relative"
        :style="{ paddingTop: safeAreaInsets.top + 30 + 'px' }"
      >
        <img
          class="absolute inset-0 bg-cover bg-center h-[300rpx]"
          :src="hospitalInfo?.header_picture"
          :style="{
            top: safeAreaInsets.top + 'px',
            width: '100vw',
            left: '0',
          }"
        />
        <text
          class="text-white text-lg absolute left-3 bg-gray-600/50 rounded px-2"
          :style="{
            top: safeAreaInsets.top + 10 + 'px',
          }"
        >
          {{ hospitalInfo?.hospital_name }}
          <!-- | 三甲医生·放心问诊 -->
        </text>
      </view>
      <!-- <view class="flex items-center bg-white rounded-full px-4 py-2">
        <wd-icon name="search" size="20px" color="#999" />
        <input type="text" placeholder="搜医生、科室、疾病、医院" class="flex-1 ml-2 text-sm" />
        <wd-icon name="mic" size="20px" color="#999" />
      </view> -->
    </view>

    <!-- Main Banner Section -->
    <view class="mx-3 relative z-10 -mt-18">
      <swiper
        class="bg-white rounded-lg h-160rpx"
        circular
        :autoplay="true"
        :interval="3000"
        :indicator-dots="true"
        indicator-color="rgba(0,0,0,0.2)"
        indicator-active-color="#42b981"
      >
        <swiper-item
          v-for="(item, index) in hospitalInfo?.roll_picture"
          :key="index"
          :style="{
            backgroundImage: `url(${item.url})`,
          }"
        >
          <!-- <view class="px-4 pt-4 pb-6">
            <view class="text-center">
              <text class="text-white font-bold text-lg">{{ item.name }}</text>
            </view>
          </view> -->
        </swiper-item>
      </swiper>
    </view>
    <!-- Health Status Section -->
    <view class="mx-3 mt-4" @click="navigateTo('/pages/webview/index')">
      <view
        class="bg-gradient-to-r from-[#42b981] to-[#4a9ff6] rounded-lg p-4 flex justify-between items-center"
      >
        <view>
          <text class="text-white font-medium">健康状况调查表</text>
          <view class="text-white/80 text-xs mt-1">定期了解您的健康状况</view>
        </view>
        <view
          class="bg-white/90 backdrop-blur text-[#42b981] py-1.5 px-4 rounded-full text-sm font-medium"
        >
          去填写
        </view>
      </view>
    </view>
    <!-- Main Services -->
    <view class="grid grid-cols-2 gap-4 mt-4 relative z-10 mx-3">
      <view
        class="bg-white rounded-lg p-4 flex items-center shadow-sm"
        v-if="showModule(Module.预约挂号)"
      >
        <view class="flex-1">
          <view class="text-base font-bold mb-1">预约挂号</view>
          <view class="text-xs text-gray-400">线下预约挂号</view>
          <view class="mt-2 bg-[#fef6e9] text-[#f5a623] text-xs px-2 py-1 rounded inline-block">
            立即预约
          </view>
        </view>
        <wd-icon name="arrow-right"></wd-icon>
      </view>
      <view
        class="bg-white rounded-lg p-4 flex items-center shadow-sm"
        v-if="showModule(Module.AI问诊)"
      >
        <view class="flex-1">
          <view class="text-base font-bold mb-1">AI问诊</view>
          <view class="text-xs text-gray-400">AI智能问诊</view>
          <view class="mt-2 bg-[#e8f5ff] text-[#42b981] text-xs px-2 py-1 rounded inline-block">
            立即问诊
          </view>
        </view>
        <wd-icon name="arrow-right"></wd-icon>
      </view>
    </view>

    <!-- Service Grid -->
    <view
      class="mx-3 mt-4 bg-white rounded-lg p-4"
      v-if="services.filter((item) => showModule(Module[item.title])).length"
    >
      <view class="grid grid-cols-4 gap-5">
        <view
          v-for="(service, index) in services.filter((item) => showModule(Module[item.title]))"
          :key="index"
          class="flex flex-col items-center"
          @click="navigateTo(service.path)"
        >
          <view class="w-12 h-12 rounded-full flex items-center justify-center bg-[#42b981]">
            <i :class="service.icon" class="text-2xl text-white"></i>
          </view>
          <view class="text-xs pt-2 text-gray-800">{{ service.title }}</view>
        </view>
      </view>
    </view>

    <!-- Directions Section -->
    <view class="mx-3 mt-4 bg-white rounded-lg p-4">
      <view class="flex items-center mb-4 justify-between">
        <view class="flex items-center">
          <text class="bg-[#42b981] w-1 h-4 rounded-lg"></text>
          <text class="text-gray-700 font-bold text-lg pl-2">来院路线</text>
        </view>
        <view class="rounded-lg bg-gray-100 px-3 py-1 text-gray-800" @click="nav">
          导航
          <i class="i-mdi:navigation-variant"></i>
        </view>
      </view>

      <!-- Hospital Info -->
      <view class="mb-4">
        <view class="flex mb-2">
          <text class="text-xs text-gray-500 w-20">医院地址：</text>
          <text class="text-xs text-gray-700 flex-1">
            {{ hospitalInfo?.navigation_location[0].address }}
          </text>
        </view>
        <!-- <view class="flex mb-2">
          <text class="text-xs text-gray-500 w-20">交通指南：</text>
          <text class="text-xs text-gray-700 flex-1">地铁2号线</text>
        </view> -->
        <view class="flex mb-2">
          <text class="text-xs text-gray-500 w-20">咨询电话：</text>
          <text class="text-xs text-gray-700 flex-1">{{ hospitalInfo?.phone_number }}</text>
        </view>
        <view class="flex mb-2">
          <text class="text-xs text-gray-500 w-20">就诊时间：</text>
          <text class="text-xs text-gray-700 flex-1">
            {{ hospitalInfo?.server_time_start }} - {{ hospitalInfo?.server_time_end }}
          </text>
        </view>
      </view>

      <!-- Map -->
      <map
        :scale="18"
        enable-zoom
        class="w-full mt-4"
        :longitude="hospitalInfo?.navigation_location[0].longitude"
        :latitude="hospitalInfo?.navigation_location[0].latitude"
      ></map>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

const { safeAreaInsets } = uni.getSystemInfoSync()
import { Data, Module } from '@/shared/home'
import { cloudHospitalInfoDetailList } from '@/service/app'
const mainBanners = reactive([
  {
    title: '日新月异医院服务中心',
    subtitle: '专注名医服务所',
  },
  {
    title: '智慧医疗服务平台',
    subtitle: '让就医更简单',
  },
  {
    title: '专业医疗团队',
    subtitle: '呵护您的健康',
  },
])

const services = reactive([
  {
    title: '预约信息',
    icon: 'i-material-symbols-calendar-today-outline-rounded',
    path: '/pages/appointment/records',
  },
  {
    title: '病历信息',
    icon: 'i-material-symbols-medical-information-outline-rounded',
    path: '/pages/medical/records',
  },
  {
    title: '检查信息',
    icon: 'i-material-symbols-stethoscope-outline-rounded',
    path: '/pages/lab/results',
  },
  {
    title: '检验信息',
    icon: 'i-material-symbols-biotech-outline-rounded',
    path: '/pages/payment/records',
  },
  {
    title: '处方信息',
    icon: 'i-material-symbols-medication-outline-rounded',
    path: '/pages/inpatient/service',
  },
  {
    title: '治疗信息',
    icon: 'i-material-symbols-medical-services-outline-rounded',
    path: '/pages/doctors/team',
  },
  {
    title: '随访记录',
    icon: 'i-material-symbols-event-repeat-outline-rounded',
    path: '/pages/reports/index',
  },
  {
    title: '电子账单',
    icon: 'i-tabler-receipt-yen',
    path: '/pages/certificates/index',
  },
])

const showModule = (moduleName: Module) => {
  return hospitalInfo.value?.module.includes(moduleName.toString())
}
const getServiceBgClass = (className: string) => {
  return className || 'bg-blue-500'
}

const navigateTo = (path: string) => {
  uni.navigateTo({ url: path })
}
const nav = () => {
  uni.getLocation({
    type: 'gcj02', // 返回可以用于 wx.openLocation 的经纬度
    success(res) {
      console.log(res)
      const latitude = res.latitude
      const longitude = res.longitude
      uni.openLocation({
        latitude,
        longitude,
        scale: 18,
        name: hospitalInfo.value?.hospital_name,
        address: hospitalInfo.value?.navigation_location[0].address,
      })
    },
    fail: (fail) => {
      console.log(fail)
    },
  })
}
let map = null
const initMap = () => {}
const hospitalInfo = ref<Data>()
onLoad(() => {
  cloudHospitalInfoDetailList({
    body: {
      hospital_id: '1',
    },
  }).then((res) => {
    const data = res.data as Data
    hospitalInfo.value = data
  })
})
onMounted(() => {
  initMap()
})
</script>

<style scoped>
.hospital-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.hospital-header {
  padding: 10px 15px;
  background-color: #333;
}

.hospital-name {
  font-size: 16px;
  font-weight: bold;
  color: white;
}

.banner-swiper {
  height: 180px;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.6));
}

.banner-content {
  color: white;
}

.banner-title {
  margin-bottom: 5px;
  font-size: 18px;
  font-weight: bold;
}

.banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.services-container {
  padding: 15px;
}

.service-row {
  display: flex;
  margin-bottom: 15px;
}

.service-item {
  display: flex;
  flex: 1;
  align-items: center;
  height: 80px;
  padding: 15px;
  color: white;
  border-radius: 10px;
}

.appointment {
  margin-right: 7.5px;
  background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.consultation {
  margin-left: 7.5px;
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.service-text {
  margin-left: 10px;
}

.service-title {
  font-size: 16px;
  font-weight: bold;
}

.service-subtitle {
  font-size: 12px;
  opacity: 0.9;
}

.service-grid {
  display: flex;
  flex-wrap: wrap;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  padding: 10px 0;
}

.grid-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
  border-radius: 50%;
}

.icon-blue {
  background-color: #2196f3;
}

.icon-green {
  background-color: #4caf50;
}

.icon-purple {
  background-color: #9c27b0;
}

.icon-cyan {
  background-color: #00bcd4;
}

.icon-orange {
  background-color: #ff9800;
}

.icon-red {
  background-color: #f44336;
}

.icon-teal {
  background-color: #009688;
}

.icon-indigo {
  background-color: #3f51b5;
}

.grid-text {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.directions-section {
  padding: 15px;
  margin: 15px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.section-header {
  padding-left: 10px;
  margin-bottom: 15px;
  border-left: 4px solid #2196f3;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.section-subtitle {
  font-size: 12px;
  color: #999;
}

.hospital-info {
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  margin-bottom: 5px;
}

.info-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.map-container {
  height: 150px;
  overflow: hidden;
  border-radius: 5px;
}

.map-image {
  width: 100%;
  height: 100%;
}

:deep(.uni-swiper-dot) {
  width: 8rpx !important;
  height: 8rpx !important;
  margin: 0 6rpx !important;
  border-radius: 50% !important;
}
</style>
