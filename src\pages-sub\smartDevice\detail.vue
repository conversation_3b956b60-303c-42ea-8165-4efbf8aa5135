<route lang="json5">
{
  style: {
    navigationBarTitleText: '穿戴设备',
  },
}
</route>
<template>
  <z-paging
    ref="paging"
    :loading-more-enabled="false"
    @query="queryList"
    :auto="false"
    :fixed="true"
    auto-show-system-loading
    :style="'top:80rpx'"
  >
    <view class="px-3">
      <view class="title text-center mt-2 flex justify-center flex-col items-center w-full">
        <view
          class="flex justify-center flex-col items-center w-full"
          v-if="
            ![HealthType.VITAL_INDEX, HealthType.HEART_INDEX, HealthType.MOOD].includes(healthType)
          "
        >
          <text class="text-lg font-bold">{{ healthTypeTitle }}</text>
          <view class="flex justify-center items-center" @click="openCalendar">
            <text class="text-sm">
              {{ dayjs(date).isValid() ? dayjs(date).format('YYYY/MM/DD') : '--' }}
            </text>
            <wd-icon name="arrow-down" size="20" color="#000" />
          </view>
        </view>
        <view
          class="flex items-center bg-white rounded-lg py-2 w-full"
          v-else-if="healthType === HealthType.MOOD"
        >
          <view class="flex items-center justify-between w-full flex-col overflow-hidden mb-2">
            <wd-tabs
              @change="handleTabChange"
              v-model="moodTab"
              :popover="{
                placement: 'bottom',
              }"
            >
              <wd-tab
                v-for="item in historyHealthData.mood.dates"
                :key="item"
                :title="`${item}`"
              ></wd-tab>
            </wd-tabs>
            <view class="flex justify-center items-center" @click="openCalendar">
              <text class="text-sm">
                {{ dayjs(date).isValid() ? dayjs(date).format('YYYY/MM/DD') : '--' }}
              </text>
              <wd-icon name="arrow-down" size="20" color="#000" />
            </view>
          </view>
        </view>
        <view
          class="flex items-center bg-white rounded-lg py-2 w-full"
          v-else-if="healthType === HealthType.VITAL_INDEX"
        >
          <view class="px-2 flex items-center justify-between w-full mb-2">
            <wd-tabs
              v-model="psychologyTab"
              @change="handleTabChange"
              style="width: 250rpx"
              :popover="{
                placement: 'bottom',
              }"
            >
              <wd-tab
                v-for="item in historyHealthData.psychology.dates"
                :key="item"
                :title="`${item}`"
              ></wd-tab>
            </wd-tabs>
            <view class="flex justify-center items-center" @click="openCalendar">
              <text class="text-sm">
                {{ dayjs(date).isValid() ? dayjs(date).format('YYYY/MM/DD') : '--' }}
              </text>
              <wd-icon name="arrow-down" size="20" color="#000" />
            </view>
          </view>
        </view>
        <view
          class="flex items-center bg-white rounded-lg py-2 w-full"
          v-else-if="healthType === HealthType.HEART_INDEX"
        >
          <view class="px-2 flex items-center justify-between w-full flex-col mb-2">
            <wd-tabs
              v-model="physiologyTab"
              @change="handleTabChange"
              :popover="{
                placement: 'bottom',
              }"
            >
              <wd-tab
                v-for="item in historyHealthData.physiology.name"
                :key="item"
                :title="`${item}`"
              ></wd-tab>
            </wd-tabs>
            <view class="flex justify-center items-center" @click="openCalendar">
              <text class="text-sm">
                {{ dayjs(date).isValid() ? dayjs(date).format('YYYY/MM/DD') : '--' }}
              </text>
              <wd-icon name="arrow-down" size="20" color="#000" />
            </view>
          </view>
        </view>
        <!-- Heart rate -->
        <HealthCard v-if="healthTypeTitle === HealthTypeTitle.HEART_RATE" class="w-full">
          <template #unit>
            <view class="flex flex-col text-left pl-3 pt-3 mb-2">
              <view class="text-2xl">
                {{ historyHealthData.heartRate.real || '--' }}
                <text class="unit">次/分钟</text>
              </view>
              <view class="text-xs text-gray-400">
                {{
                  dayjs(historyHealthData.heartRate.time).isValid()
                    ? dayjs(historyHealthData.heartRate.time).format('MM/DD HH:mm')
                    : '--'
                }}
              </view>
            </view>
          </template>
          <template #content>
            <!-- <view class="chart-container" ref="heartHistoryRef"></view> -->
            <view class="w-[calc(100vw-60rpx)] h-[180rpx]">
              <JnChart
                canvasId="heartHistoryRef"
                :option="getLineOptions(historyHealthData.heartRate)"
                lazyLoad
              />
            </view>
          </template>
        </HealthCard>

        <!-- Blood oxygen -->
        <HealthCard v-if="healthTypeTitle === HealthTypeTitle.BLOOD_OXYGEN" class="w-full">
          <template #unit>
            <view class="flex flex-col text-left pl-3 pt-3 mb-2">
              <view class="text-2xl">
                {{ historyHealthData.bloodOxygen.real || '--' }}
                <text class="unit">%</text>
              </view>
              <view class="text-xs text-gray-400">
                {{
                  dayjs(historyHealthData.bloodOxygen.time).isValid()
                    ? dayjs(historyHealthData.bloodOxygen.time).format('MM/DD HH:mm')
                    : '--'
                }}
              </view>
            </view>
          </template>
          <template #content>
            <view class="w-[calc(100vw-60rpx)] h-[180rpx]">
              <JnChart
                canvasId="bloodOxygenHistoryRef"
                :option="getLineOptions(historyHealthData.bloodOxygen)"
                lazyLoad
              />
            </view>
          </template>
        </HealthCard>

        <!-- Temperature -->
        <HealthCard v-if="healthTypeTitle === HealthTypeTitle.TEMPERATURE" class="w-full">
          <template #unit>
            <view class="flex flex-col text-left pl-3 pt-3 mb-2">
              <view class="text-2xl">
                {{ historyHealthData.temperature.real || '--' }}
                <text class="unit">°C</text>
              </view>
              <view class="text-xs text-gray-400">
                {{
                  dayjs(historyHealthData.temperature.time).isValid()
                    ? dayjs(historyHealthData.temperature.time).format('MM/DD HH:mm')
                    : '--'
                }}
              </view>
            </view>
          </template>
          <template #content>
            <view class="w-[calc(100vw-60rpx)] h-[180rpx]">
              <JnChart
                canvasId="temperatureHistoryRef"
                :option="getLineOptions(historyHealthData.temperature)"
                lazyLoad
              />
            </view>
          </template>
        </HealthCard>

        <!-- Blood pressure -->
        <HealthCard v-if="healthTypeTitle === HealthTypeTitle.BLOOD_PRESSURE" class="w-full">
          <template #unit>
            <view class="flex flex-col text-left pl-3 pt-3 mb-2">
              <view class="text-2xl">
                {{ historyHealthData.bloodPressure.sbp.real || '--' }}/
                <text>
                  {{ historyHealthData.bloodPressure.dbp.real || '--' }}
                </text>
                <text class="unit">mmHg</text>
              </view>
              <view class="text-xs text-gray-400">
                {{
                  dayjs(historyHealthData.bloodPressure.sbp.time).isValid()
                    ? dayjs(historyHealthData.bloodPressure.sbp.time).format('MM/DD HH:mm')
                    : '--'
                }}
              </view>
            </view>
          </template>
          <template #content>
            <view class="w-[calc(100vw-60rpx)] h-[180rpx]">
              <JnChart
                canvasId="bloodPressureHistoryRef"
                :option="getBloodPressureLineOptions(historyHealthData.bloodPressure)"
                lazyLoad
              />
            </view>
          </template>
        </HealthCard>

        <!-- Mood -->
        <HealthCard v-if="healthTypeTitle === HealthTypeTitle.MOOD" class="col-span-2 w-full">
          <template #unit>
            <view class="flex flex-col text-left pl-3 pt-3 mb-2">
              <view class="text-2xl">
                <!-- 假设情绪数据是一个数值，需根据实际数据调整 -->
                <!-- {{ historyHealthData.mood.real || '--' }} -->
                <!-- <text class="unit">分</text> -->
              </view>
              <view class="text-xs text-gray-400">
                {{
                  dayjs(historyHealthData.mood.time).isValid()
                    ? dayjs(historyHealthData.mood.time).format('MM/DD HH:mm')
                    : '--'
                }}
              </view>
            </view>
          </template>
          <template #content>
            <view class="w-[calc(100vw-60rpx)] h-[180rpx]">
              <JnChart
                canvasId="moodRef"
                :option="getMoodLineOptions(historyHealthData.mood, moodTab)"
                lazyLoad
              />
            </view>
          </template>
        </HealthCard>

        <!-- Vital index -->
        <HealthCard
          v-if="healthTypeTitle === HealthTypeTitle.VITAL_INDEX"
          class="col-span-2 pb-10 w-full"
        >
          <template #unit>
            <view class="flex flex-col text-left pl-3 pt-3 mb-2">
              <view class="text-2xl">
                <!-- 假设心理指标是一个综合值，需根据实际数据调整 -->
                {{
                  psychologyTab === 0
                    ? historyHealthData.psychology.pressure.real || '--'
                    : psychologyTab === 1
                      ? historyHealthData.psychology.anxiety.real || '--'
                      : historyHealthData.psychology.depression.real || '--'
                }}

                <text class="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-1">
                  {{
                    psychologyTab === 0
                      ? getHealthLevel(historyHealthData.psychology.pressure.real)
                      : psychologyTab === 1
                        ? getHealthLevel(historyHealthData.psychology.anxiety.real)
                        : getHealthLevel(historyHealthData.psychology.depression.real)
                  }}
                </text>
              </view>
              <view class="text-xs text-gray-400 mt-1">
                {{
                  dayjs(
                    psychologyTab === 0
                      ? historyHealthData.psychology.pressure.time
                      : psychologyTab === 1
                        ? historyHealthData.psychology.anxiety.time
                        : historyHealthData.psychology.depression.time,
                  ).isValid()
                    ? dayjs(
                        psychologyTab === 0
                          ? historyHealthData.psychology.pressure.time
                          : psychologyTab === 1
                            ? historyHealthData.psychology.anxiety.time
                            : historyHealthData.psychology.depression.time,
                      ).format('MM/DD HH:mm')
                    : '--'
                }}
              </view>
            </view>
          </template>
          <template #content>
            <view class="w-[calc(100vw-60rpx)] h-[180rpx]">
              <JnChart
                canvasId="psychologyLineRef"
                :option="getPsychologyLineOptions(historyHealthData.psychology, psychologyTab)"
                lazyLoad
              />
            </view>
            <view class="flex items-center justify-between px-3 pt-4 text-gray-400 text-sm mt-2">
              <view>0-29 正常</view>
              <view>30-59 轻度</view>
              <view>60-79 中度</view>
              <view>80-100 重度</view>
            </view>
          </template>
        </HealthCard>

        <!-- Heart index -->
        <HealthCard v-if="healthTypeTitle === HealthTypeTitle.HEART_INDEX" class="col-span-2 pb-10">
          <template #unit>
            <view class="flex flex-col text-left pl-3 pt-3 mb-2">
              <view class="text-2xl">
                <!-- 假设生理指标是一个综合值，需根据实际数据调整 -->
                {{
                  physiologyTab === 0
                    ? historyHealthData.physiology.af.real || '--'
                    : psychologyTab === 1
                      ? historyHealthData.physiology.sa.real || '--'
                      : historyHealthData.physiology.bv.real || '--'
                }}

                <text class="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-1">
                  {{
                    physiologyTab === 0
                      ? getHealthLevel(historyHealthData.physiology.af.real)
                      : physiologyTab === 1
                        ? getHealthLevel(historyHealthData.physiology.sa.real)
                        : getHealthLevel(historyHealthData.physiology.bv.real)
                  }}
                </text>
              </view>
              <view class="text-xs text-gray-400 mt-1">
                {{
                  dayjs(
                    physiologyTab === 0
                      ? historyHealthData.physiology.af.time
                      : physiologyTab === 1
                        ? historyHealthData.physiology.sa.time
                        : historyHealthData.physiology.bv.time,
                  ).isValid()
                    ? dayjs(
                        physiologyTab === 0
                          ? historyHealthData.physiology.af.time
                          : physiologyTab === 1
                            ? historyHealthData.physiology.sa.time
                            : historyHealthData.physiology.bv.time,
                      ).format('MM/DD HH:mm')
                    : '--'
                }}
              </view>
            </view>
          </template>
          <template #content>
            <view class="w-[calc(100vw-60rpx)] h-[180rpx]">
              <JnChart
                canvasId="physiologyLineRef"
                :option="getPhysiologyLineOptions(historyHealthData.physiology, physiologyTab)"
                lazyLoad
              />
            </view>
            <view class="flex items-center justify-between px-3 pt-2 mt-4 text-gray-400 text-sm">
              <view>0-29 正常</view>
              <view>30-59 轻度</view>
              <view>60-79 中度</view>
              <view>80-100 重度</view>
            </view>
          </template>
        </HealthCard>
      </view>

      <StaticData
        :data="getStaticData"
        :title="healthTypeTitle"
        :type="healthType"
        :unit="getUnit"
        :tab="
          healthType === HealthType.MOOD
            ? moodTab
            : healthType === HealthType.VITAL_INDEX
              ? psychologyTab
              : physiologyTab
        "
      />

      <wd-popup
        v-model="showCalendar"
        position="center"
        closable
        custom-style="border-radius:20rpx;width:90vw; "
      >
        <view class="px-7 pt-7 pb-4">
          <wd-button class="mt-4" @click="handleConfirm" block type="primary">确定</wd-button>
        </view>
      </wd-popup>
    </view>
  </z-paging>
</template>
<script lang="ts" setup>
import {
  HealthType,
  HealthTypeTitle,
  getHealthLevel,
  HealthDataType,
  HealthTypeToDataType,
  formateDate,
} from './shared'
import dayjs from 'dayjs'

import { watchApi } from '@/service/index/watch'
import zPaging from '@/uni_modules/z-paging/components/z-paging/z-paging.vue'
import HealthCard from '../components/HealthCard.vue'
import JnChart from '../components/jn-chart/index.vue'
import StaticData from '../components/staticData.vue'
import { useHealthCharts } from './useCharts'

const paging = ref(null)
const queryList = () => {
  console.log('dad')
  paging.value.complete([{ id: 1 }])
}
const showCalendar = ref(false)

const highlight = ref(new Date())
const attrs = ref<any[]>([
  { key: 'today', highlight: true, dates: highlight.value },
  { key: 'data', dot: { color: 'blue' }, dates: [] },
])
// Initialize health charts
const {
  heartHistoryRef,
  bloodOxygenHistoryRef,
  temperatureHistoryRef,
  bloodPressureHistoryRef,
  bloodOxygenRef,
  temperatureRef,
  bloodPressureRef,
  moodRef,
  psychologyLineRef,
  physiologyLineRef,
  getHistoryData,
  physiologyRef,
  historyHealthData,
  getLineOptions,
  getBloodPressureLineOptions,
  getMoodLineOptions,
  getPsychologyLineOptions,
  getPhysiologyLineOptions,
  initHistoryCharts,
} = useHealthCharts()

const healthTypeTitle = ref('')

const healthType = ref<HealthType>(HealthType.HEART_RATE)
const moodTab = ref(0)
const date = ref<number>(Date.now())
const handleConfirm = () => {
  const day = attrs.value[0].dates[0]
  date.value = day
  showCalendar.value = false
  getHistoryData(dayjs(day).format('YYYY/MM/DD'), dayjs(day).format('YYYY/MM/DD'), moodTab.value)
}
const handleTabChange = ({ index, name }: { index: number; name: string }) => {
  initHistoryCharts(index)
}
const handleDayclick = ({ date, type }) => {
  attrs.value[0].dates = [dayjs(date).format('YYYY/MM/DD')]
}
const openCalendar = () => {
  showCalendar.value = true
  getMonthData(dayjs(date.value).format('YYYY/MM'))
}
const handleUpdatePages = (pages) => {
  const page = pages[0]
  const { year, month, view, viewDays } = page
  console.log(page)
  getMonthData(`${year}/${month}`)
}
const psychologyTab = ref(0)
const physiologyTab = ref(0)
const getUnit = computed(() => {
  if (healthType.value === HealthType.HEART_RATE) {
    return '次/分钟'
  }
  if (healthType.value === HealthType.BLOOD_OXYGEN) {
    return '%'
  }
  if (healthType.value === HealthType.TEMPERATURE) {
    return '°C'
  }
  if (healthType.value === HealthType.BLOOD_PRESSURE) {
    return 'mmHg'
  }
  if (healthType.value === HealthType.MOOD) {
    return '%'
  }
})

const getStaticData = computed(() => {
  if (healthType.value === HealthType.HEART_RATE) {
    return historyHealthData.heartRate
  }
  if (healthType.value === HealthType.BLOOD_OXYGEN) {
    return historyHealthData.bloodOxygen
  }
  if (healthType.value === HealthType.TEMPERATURE) {
    return historyHealthData.temperature
  }
  if (healthType.value === HealthType.BLOOD_PRESSURE) {
    return historyHealthData.bloodPressure
  }
  if (healthType.value === HealthType.MOOD) {
    return historyHealthData.mood
  }
  if (healthType.value === HealthType.VITAL_INDEX) {
    return historyHealthData.psychology
  }
  if (healthType.value === HealthType.HEART_INDEX) {
    return historyHealthData.physiology
  }
})
const getTypeData = (type: HealthType) => {
  if (type === HealthType.HEART_RATE) {
    return HealthTypeToDataType.HEART_RATE
  }
  if (type === HealthType.BLOOD_OXYGEN) {
    return HealthTypeToDataType.BLOOD_OXYGEN
  }
  if (type === HealthType.TEMPERATURE) {
    return HealthTypeToDataType.TEMPERATURE
  }
  if (type === HealthType.BLOOD_PRESSURE) {
    return HealthTypeToDataType.BLOOD_PRESSURE
  }
  if (type === HealthType.MOOD) {
    return HealthTypeToDataType.MOOD
  }
  if (type === HealthType.VITAL_INDEX) {
    return ['pressure', 'anxiety', 'depression'][psychologyTab.value]
  }
  if (type === HealthType.HEART_INDEX) {
    return ['af', 'sa', 'bv'][physiologyTab.value]
  }
}
const getMonthData = (month: string) => {
  watchApi
    .getMonthInfo({
      month,
      // user_id: userStore.userInfo.user_id,
      user_id: '20240425000712',
      type: getTypeData(healthType.value),
    })
    .then((res) => {
      const dates = Object.values(res.data)
      console.log(dates)
      attrs.value[1].dates = dates
    })
}
onReady(() => {})
onLoad((options) => {
  const type = options.type as HealthType
  console.log(type)
  healthTypeTitle.value = HealthTypeTitle[type]
  healthType.value = type
  getHistoryData(
    dayjs(date.value).format('YYYY/MM/DD'),
    dayjs(date.value).format('YYYY/MM/DD'),
    moodTab.value,
  )
  getMonthData(dayjs(date.value).format('YYYY/MM'))
})
</script>
<style>
page {
  background: #f2f2f2;
}
</style>
<style lang="scss" scoped>
.page-container {
  padding: 16px;
}

.health-overview {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-text {
  margin-top: 12px;
  font-size: 14px;
  color: #666;
}

.top-reminder {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #f56c6c;
  background-color: rgba(255, 229, 229, 0.6);
  border-radius: 8px;
}

.bind-btn {
  margin-left: 16px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.unit {
  font-size: 12px;
  color: #999;
}

.icon-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;

  &--heart {
    background-color: #f56c6c;
  }

  &--blood-oxygen {
    background-color: #409eff;
  }

  &--temperature {
    background-color: #67c23a;
  }

  &--blood-pressure {
    background-color: #e6a23c;
  }

  &--sleep {
    background-color: #f56c6c;
  }
}
.health-card {
  width: 100%;
  margin-top: 20rpx;
  :deep(.health-card__content) {
    height: 380rpx !important;
    min-height: 280rpx !important;
  }
}

:deep(.wd-tabs) {
  border: 0;
}
:deep(.wd-tabs__nav-item-text) {
  font-size: 28rpx;
}
</style>
