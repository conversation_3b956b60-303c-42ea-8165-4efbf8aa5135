/**
 * useFilePreview - 文件预览功能钩子
 * 支持图片、视频、文档、富文本等多种文件类型的预览
 */

/**
 * 获取文件类型
 */
export const getFileType = (url: string, extension?: string): FileType => {
  const ext = extension || url.split('.').pop()?.toLowerCase() || ''
  
  // 图片类型
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
    return 'image'
  }
  
  // 视频类型
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'm4v', '3gp'].includes(ext)) {
    return 'video'
  }
  
  // 文档类型
  if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
    return 'document'
  }
  
  // 富文本类型
  if (['html', 'htm'].includes(ext)) {
    return 'richtext'
  }
  
  return 'document' // 默认作为文档处理
}

/**
 * 图片预览
 */
export const previewImages = (options: PreviewOptions) => {
  const imageFiles = options.files.filter(file => file.type === 'image')
  
  if (imageFiles.length === 0) {
    uni.showToast({
      title: '没有可预览的图片',
      icon: 'none'
    })
    return
  }
  
  const urls = imageFiles.map(file => file.url)
  const current = options.current || 0
  
  uni.previewImage({
    urls,
    current: urls[current],
    longPressActions: {
      itemList: ['保存图片'],
      success: (data) => {
        if (data.tapIndex === 0) {
          // 保存图片到相册
          uni.saveImageToPhotosAlbum({
            filePath: urls[data.index],
            success: () => {
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: () => {
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              })
            }
          })
        }
      }
    }
  })
}

/**
 * 视频预览
 */
export const previewVideo = (file: FileItem) => {
  uni.navigateTo({
    url: `/pages/file-preview/index?type=video&url=${encodeURIComponent(file.url)}&name=${encodeURIComponent(file.name)}`
  })
}

/**
 * 文档预览
 */
export const previewDocument = (file: FileItem) => {
  const extension = file.extension || file.url.split('.').pop()?.toLowerCase()
  
  // PDF文件使用原生预览
  if (extension === 'pdf') {
    uni.downloadFile({
      url: file.url,
      success: (res) => {
        if (res.statusCode === 200) {
          uni.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            success: () => {
              console.log('PDF预览成功')
            },
            fail: (err) => {
              console.error('PDF预览失败:', err)
              uni.showToast({
                title: '预览失败',
                icon: 'none'
              })
            }
          })
        }
      },
      fail: (err) => {
        console.error('文件下载失败:', err)
        uni.showToast({
          title: '文件下载失败',
          icon: 'none'
        })
      }
    })
  } else {
    // 其他文档类型跳转到预览页面
    uni.navigateTo({
      url: `/pages/file-preview/index?type=document&url=${encodeURIComponent(file.url)}&name=${encodeURIComponent(file.name)}&extension=${extension}`
    })
  }
}

/**
 * 富文本预览
 */
export const previewRichText = (content: string, title?: string) => {
  uni.navigateTo({
    url: `/pages/file-preview/index?type=richtext&content=${encodeURIComponent(content)}&title=${encodeURIComponent(title || '富文本内容')}`
  })
}

/**
 * 统一文件预览入口
 */
export const previewFile = (file: FileItem, options?: Partial<PreviewOptions>) => {
  const fileType = file.type || getFileType(file.url, file.extension)
  
  switch (fileType) {
    case 'image':
      previewImages({
        files: [file],
        current: 0,
        ...options
      })
      break
    case 'video':
      previewVideo(file)
      break
    case 'document':
      previewDocument(file)
      break
    case 'richtext':
      // 富文本需要传入内容，这里暂时跳转到预览页面
      uni.navigateTo({
        url: `/pages/file-preview/index?type=richtext&url=${encodeURIComponent(file.url)}&name=${encodeURIComponent(file.name)}`
      })
      break
    default:
      uni.showToast({
        title: '不支持的文件类型',
        icon: 'none'
      })
  }
}

/**
 * 批量文件预览
 */
export const previewFiles = (options: PreviewOptions) => {
  if (options.files.length === 0) {
    uni.showToast({
      title: '没有可预览的文件',
      icon: 'none'
    })
    return
  }
  
  if (options.files.length === 1) {
    previewFile(options.files[0], options)
    return
  }
  
  // 多个文件时，根据类型分组处理
  const imageFiles = options.files.filter(file => file.type === 'image')
  
  if (imageFiles.length > 0 && imageFiles.length === options.files.length) {
    // 全部是图片，使用图片预览
    previewImages(options)
  } else {
    // 混合类型，跳转到文件列表页面
    const filesData = encodeURIComponent(JSON.stringify(options.files))
    uni.navigateTo({
      url: `/pages/file-preview/index?type=list&files=${filesData}&current=${options.current || 0}`
    })
  }
}

export default {
  getFileType,
  previewImages,
  previewVideo,
  previewDocument,
  previewRichText,
  previewFile,
  previewFiles
}
