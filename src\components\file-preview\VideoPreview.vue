<template>
  <view class="video-preview">
    <view class="video-container" @click="handleVideoClick">
      <video
        
        :src="videoFile.url"
        :poster="videoFile.thumbnail"
        :controls="controls"
        :autoplay="autoplay"
        :loop="loop"
        :muted="muted"
        :show-center-play-btn="showCenterPlayBtn"
        :show-play-btn="showPlayBtn"
        :show-fullscreen-btn="showFullscreenBtn"
        :show-progress="showProgress"
        :enable-progress-gesture="enableProgressGesture"
        :object-fit="objectFit"
        class="video-player"
        @play="handlePlay"
        @pause="handlePause"
        @ended="handleEnded"
        @error="handleError"
        @fullscreenchange="handleFullscreenChange"
      />
      
      <!-- 自定义播放按钮 -->
      <view v-if="!showVideo && !autoplay" class="play-overlay" @click="startPlay">
        <view class="play-button">
          <i class="i-carbon-play-filled text-white text-4xl"></i>
        </view>
        <view v-if="videoFile.name" class="video-title">
          {{ videoFile.name }}
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-overlay">
        <wd-loading type="circular" />
        <text class="loading-text">视频加载中...</text>
      </view>
    </view>
    
    <!-- 视频信息 -->
    <!-- <view v-if="showInfo && videoFile.name" class="video-info">
      <view class="info-row">
        <text class="info-label">文件名：</text>
        <text class="info-value">{{ videoFile.name }}</text>
      </view>
      <view v-if="videoFile.size" class="info-row">
        <text class="info-label">文件大小：</text>
        <text class="info-value">{{ formatFileSize(videoFile.size) }}</text>
      </view>
    </view> -->
    
    <!-- 操作按钮 -->
    <!-- <view v-if="showActions" class="action-buttons">
      <wd-button 
        type="primary" 
        size="small"
        @click="downloadVideo"
      >
        下载视频
      </wd-button>
      <wd-button 
        type="default" 
        size="small"
        @click="shareVideo"
      >
        分享
      </wd-button>
    </view> -->
  </view>
</template>

<script setup lang="ts">
interface Props {
  videoFile: FileItem
  controls?: boolean
  autoplay?: boolean
  loop?: boolean
  muted?: boolean
  showCenterPlayBtn?: boolean
  showPlayBtn?: boolean
  showFullscreenBtn?: boolean
  showProgress?: boolean
  enableProgressGesture?: boolean
  objectFit?: 'contain' | 'fill' | 'cover'
  showInfo?: boolean
  showActions?: boolean
  clickToPlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  controls: true,
  autoplay: false,
  loop: false,
  muted: false,
  showCenterPlayBtn: true,
  showPlayBtn: true,
  showFullscreenBtn: true,
  showProgress: true,
  enableProgressGesture: true,
  objectFit: 'contain',
  showInfo: true,
  showActions: true,
  clickToPlay: true
})

const showVideo = ref(props.autoplay)
const loading = ref(false)
const isPlaying = ref(false)

// 开始播放
const startPlay = () => {
  showVideo.value = true
  loading.value = true
}

// 视频容器点击事件
const handleVideoClick = () => {
  if (props.clickToPlay && !showVideo.value) {
    startPlay()
  }
}

// 播放事件
const handlePlay = () => {
  isPlaying.value = true
  loading.value = false
  console.log('视频开始播放')
}

// 暂停事件
const handlePause = () => {
  isPlaying.value = false
  console.log('视频暂停')
}

// 播放结束事件
const handleEnded = () => {
  isPlaying.value = false
  console.log('视频播放结束')
}

// 错误事件
const handleError = (e: any) => {
  loading.value = false
  console.error('视频播放错误:', e)
  uni.showToast({
    title: '视频播放失败',
    icon: 'none'
  })
}

// 全屏状态改变
const handleFullscreenChange = (e: any) => {
  console.log('全屏状态改变:', e)
}

// 下载视频
const downloadVideo = () => {
  uni.showLoading({
    title: '下载中...'
  })
  
  uni.downloadFile({
    url: props.videoFile.url,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        uni.saveVideoToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: (err) => {
            console.error('保存视频失败:', err)
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        })
      }
    },
    fail: (err) => {
      uni.hideLoading()
      console.error('下载视频失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  })
}

// 分享视频
const shareVideo = () => {
  // #ifdef MP-WEIXIN
  uni.share({
    provider: 'weixin',
    type: 2,
    videoPath: props.videoFile.url,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: (err) => {
      console.error('分享失败:', err)
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  })
  // #endif
  
  // #ifndef MP-WEIXIN
  uni.showToast({
    title: '当前平台不支持分享',
    icon: 'none'
  })
  // #endif
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}
</script>

<style scoped lang="scss">
.video-preview {
  .video-container {
    position: relative;
    width: 100%;
    background: #000;
    border-radius: 8rpx;
    overflow: hidden;
    
    .video-player {
      width: 100%;
      height: auto;
      min-height: 400rpx;
    }
    
    .play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.5);
      min-height: 400rpx;
      
      .play-button {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20rpx;
        transition: all 0.3s ease;
        
        &:active {
          transform: scale(0.95);
          background: rgba(255, 255, 255, 0.3);
        }
      }
      
      .video-title {
        color: white;
        font-size: 28rpx;
        text-align: center;
        padding: 0 20rpx;
      }
    }
    
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.7);
      
      .loading-text {
        color: white;
        font-size: 28rpx;
        margin-top: 20rpx;
      }
    }
  }
  
  .video-info {
    margin-top: 20rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    
    .info-row {
      display: flex;
      margin-bottom: 10rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        color: #666;
        font-size: 28rpx;
        min-width: 140rpx;
      }
      
      .info-value {
        color: #333;
        font-size: 28rpx;
        flex: 1;
      }
    }
  }
  
  .action-buttons {
    margin-top: 20rpx;
    display: flex;
    gap: 20rpx;
    justify-content: center;
  }
}
</style>
