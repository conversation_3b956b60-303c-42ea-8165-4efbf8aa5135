/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/appointment/index" |
       "/pages/cloudClassRoom/article" |
       "/pages/cloudClassRoom/index" |
       "/pages/consultation/index" |
       "/pages/demo/file-preview" |
       "/pages/file-preview/index" |
       "/pages/login/index" |
       "/pages/personal/index" |
       "/pages/server/index" |
       "/pages/test/preview" |
       "/pages/webview/index" |
       "/pages-sub/smartDevice/detail" |
       "/pages-sub/smartDevice/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/cloudClassRoom/index" | "/pages/server/index" | "/pages/personal/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
