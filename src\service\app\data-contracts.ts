/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export type IndexIndexListData = object;

export interface IndexLoginCreatePayload {
  /** @example "admin" */
  username?: string;
  /** @example "123456" */
  password?: string;
  /** @example "true" */
  keep?: string;
}

export interface IndexLoginCreateData {
  code: number;
  msg: string;
  time: number;
  data: {
    userInfo: {
      id: number;
      username: string;
      nickname: string;
      avatar: string;
      last_login_time: string;
      token: string;
      refresh_token: string;
    };
  };
}

export interface CloudHospitalInfoCreateHospitalInfoCreatePayload {
  /** 医院id */
  hospital_id?: number;
  /** 医院名称 */
  hospital_name?: string;
  /** 医院地址 */
  hospital_address?: string;
  /** 咨询热线 */
  phone_number?: string;
  /** 服务开始日期 */
  server_day_start?: string;
  /** 服务结束日期 */
  server_day_end?: string;
  /** 服务开始时间 */
  server_time_start?: string;
  /** 服务结束时间 */
  server_time_end?: string;
  /** 导航位置 */
  navigation_location?: string;
  /** 头图图片 */
  header_picture?: string;
  /** 滚图设置 */
  roll_picture?: any[];
  /** 模块 */
  module: any[];
}

export type CloudHospitalInfoCreateHospitalInfoCreateData = object;

export interface CloudHospitalInfoDetailListPayload {
  /** 医院id */
  hospital_id?: string;
}

export type CloudHospitalInfoDetailListData = object;

export type CloudClassroomCategoryCategoryListListData = object;

export type CloudClassroomCategoryIndexListData = object;

export interface CloudClassroomCategoryCreateCategoryCreatePayload {
  /** 分类名称 */
  title?: string;
  /** 排序 */
  sort?: number;
}

export type CloudClassroomCategoryCreateCategoryCreateData = object;

export interface CloudClassroomCategoryUpdateCategoryCreatePayload {
  id?: string;
  /** 分类名称 */
  title?: string;
  /** 排序 */
  sort?: string;
  /** 状态 0 关闭;1开启 */
  status?: string;
}

export type CloudClassroomCategoryUpdateCategoryCreateData = object;

export interface CloudClassroomCategoryDelCategoryCreatePayload {
  id?: string;
}

export type CloudClassroomCategoryDelCategoryCreateData = object;

export interface CloudClassroomCourseIndexListParams {
  /** 标题 */
  title?: string;
  /** 分类id */
  category_id?: string;
  /** 状态 */
  status?: string;
  /** 置顶 */
  is_top?: string;
  /** 热门 */
  is_hot?: string;
  limit?: string;
  page?: string;
}

export type CloudClassroomCourseIndexListData = object;

export interface CloudClassroomCourseCreateCourseCreatePayload {
  /** 分类id */
  category_id?: string;
  /** 标题 */
  title?: string;
  /** 类型 */
  type?: string;
  /** 内容 */
  content?: string;
}

export type CloudClassroomCourseCreateCourseCreateData = object;

export interface CloudClassroomCourseUpdateCourseCreatePayload {
  category_id?: string;
  title?: string;
  type?: string;
  content?: string;
  /** 排序 */
  sort?: string;
}

export type CloudClassroomCourseUpdateCourseCreateData = object;

export interface CloudClassroomCourseChangeIsHotCreatePayload {
  id?: string;
  /** 1热门 0非热门 */
  is_hot?: string;
}

export type CloudClassroomCourseChangeIsHotCreateData = object;

export interface CloudClassroomCourseChangeIsTopCreatePayload {
  id?: string;
  /** 1置顶 0 非置顶 */
  is_top?: string;
}

export type CloudClassroomCourseChangeIsTopCreateData = object;

export interface CloudClassroomCourseChangeIsOffCreatePayload {
  id?: string;
  /** 1 下架 0未下架 */
  is_off?: string;
}

export type CloudClassroomCourseChangeIsOffCreateData = object;

export interface CloudClassroomCourseChangeStatusCreatePayload {
  id?: string;
  /** 1启用 0 停用 */
  status?: string;
}

export type CloudClassroomCourseChangeStatusCreateData = object;

export interface CloudClassroomCategoryChangeCategoryStatusCreatePayload {
  id?: string;
  /** 状态 */
  status?: string;
}

export type CloudClassroomCategoryChangeCategoryStatusCreateData = object;

export interface CloudClassroomCategoryChangeCategorySortCreatePayload {
  id?: string;
  sort?: string;
}

export type CloudClassroomCategoryChangeCategorySortCreateData = object;

export interface CloudClassroomCourseDelCourseCreatePayload {
  id?: string;
}

export type CloudClassroomCourseDelCourseCreateData = object;

export interface CloudClassroomCourseChangeSortCreatePayload {
  id?: string;
  sort?: string;
}

export type CloudClassroomCourseChangeSortCreateData = object;

export type CommonSmsCodeSendSmsCodeCreateData = object;

export interface IndexCodeLoginCreatePayload {
  /** 验证码ID */
  id?: string;
  /** 验证码内容 */
  code?: string;
  /** 手机号 */
  phone?: string;
}

export type IndexCodeLoginCreateData = object;
