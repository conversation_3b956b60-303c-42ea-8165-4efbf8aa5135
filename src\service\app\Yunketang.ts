/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                          ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
// @ts-ignore
import { CustomRequestOptions } from "@/interceptors/request";
import request from "@/utils/request";

/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/* eslint-disable */
// @ts-ignore
import {
  CloudClassroomCategoryCategoryListListData,
  CloudClassroomCategoryChangeCategorySortCreateData,
  CloudClassroomCategoryChangeCategorySortCreatePayload,
  CloudClassroomCategoryChangeCategoryStatusCreateData,
  CloudClassroomCategoryChangeCategoryStatusCreatePayload,
  CloudClassroomCategoryCreateCategoryCreateData,
  CloudClassroomCategoryCreateCategoryCreatePayload,
  CloudClassroomCategoryDelCategoryCreateData,
  CloudClassroomCategoryDelCategoryCreatePayload,
  CloudClassroomCategoryIndexListData,
  CloudClassroomCategoryUpdateCategoryCreateData,
  CloudClassroomCategoryUpdateCategoryCreatePayload,
  CloudClassroomCourseChangeIsHotCreateData,
  CloudClassroomCourseChangeIsHotCreatePayload,
  CloudClassroomCourseChangeIsOffCreateData,
  CloudClassroomCourseChangeIsOffCreatePayload,
  CloudClassroomCourseChangeIsTopCreateData,
  CloudClassroomCourseChangeIsTopCreatePayload,
  CloudClassroomCourseChangeSortCreateData,
  CloudClassroomCourseChangeSortCreatePayload,
  CloudClassroomCourseChangeStatusCreateData,
  CloudClassroomCourseChangeStatusCreatePayload,
  CloudClassroomCourseCreateCourseCreateData,
  CloudClassroomCourseCreateCourseCreatePayload,
  CloudClassroomCourseDelCourseCreateData,
  CloudClassroomCourseDelCourseCreatePayload,
  CloudClassroomCourseIndexListData,
  CloudClassroomCourseUpdateCourseCreateData,
  CloudClassroomCourseUpdateCourseCreatePayload,
  CloudHospitalInfoCreateHospitalInfoCreateData,
  CloudHospitalInfoCreateHospitalInfoCreatePayload,
  CloudHospitalInfoDetailListData,
  CloudHospitalInfoDetailListPayload,
} from "./data-contracts";

// yunketang 模块接口
/**
 * No description *
 * @tags 云课堂
 * @name CloudHospitalInfoCreateHospitalInfoCreate
 * @summary 医院信息填写
 * @request POST:/admin/cloud.HospitalInfo/createHospitalInfo */
export async function cloudHospitalInfoCreateHospitalInfoCreate({
  body,
  options,
}: {
  body: CloudHospitalInfoCreateHospitalInfoCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudHospitalInfoCreateHospitalInfoCreateData>(
    "/admin/cloud.HospitalInfo/createHospitalInfo",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudHospitalInfoDetailList
 * @summary 医院信息详情
 * @request GET:/admin/cloud.HospitalInfo/detail */
export async function cloudHospitalInfoDetailList({
  body,
  options,
}: {
  body: CloudHospitalInfoDetailListPayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudHospitalInfoDetailListData>(
    "/admin/cloud.HospitalInfo/detail",
    {
      method: "GET",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCategoryCategoryListList
 * @summary 云课堂创建分类选择
 * @request GET:/admin/cloud.ClassroomCategory/categoryList */
export async function cloudClassroomCategoryCategoryListList({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCategoryCategoryListListData>(
    "/admin/cloud.ClassroomCategory/categoryList",
    {
      method: "GET",
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCategoryIndexList
 * @summary 云课堂分类列表
 * @request GET:/admin/cloud.ClassroomCategory/index */
export async function cloudClassroomCategoryIndexList({
  options,
}: {
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCategoryIndexListData>(
    "/admin/cloud.ClassroomCategory/index",
    {
      method: "GET",
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCategoryCreateCategoryCreate
 * @summary 云课堂分类创建
 * @request POST:/admin/cloud.ClassroomCategory/createCategory */
export async function cloudClassroomCategoryCreateCategoryCreate({
  body,
  options,
}: {
  body: CloudClassroomCategoryCreateCategoryCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCategoryCreateCategoryCreateData>(
    "/admin/cloud.ClassroomCategory/createCategory",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCategoryUpdateCategoryCreate
 * @summary 云课堂分类编辑
 * @request POST:/admin/cloud.ClassroomCategory/updateCategory */
export async function cloudClassroomCategoryUpdateCategoryCreate({
  body,
  options,
}: {
  body: CloudClassroomCategoryUpdateCategoryCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCategoryUpdateCategoryCreateData>(
    "/admin/cloud.ClassroomCategory/updateCategory",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCategoryDelCategoryCreate
 * @summary 云课堂删除分类
 * @request POST:/admin/cloud.ClassroomCategory/delCategory */
export async function cloudClassroomCategoryDelCategoryCreate({
  body,
  options,
}: {
  body: CloudClassroomCategoryDelCategoryCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCategoryDelCategoryCreateData>(
    "/admin/cloud.ClassroomCategory/delCategory",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseIndexList
 * @summary 课程列表
 * @request GET:/admin/cloud.ClassroomCourse/index */
export async function cloudClassroomCourseIndexList({
  query,
  options,
}: {
  query: {
    /** 标题 */
    title?: string;
    /** 分类id */
    category_id?: string;
    /** 状态 */
    status?: string;
    /** 置顶 */
    is_top?: string;
    /** 热门 */
    is_hot?: string;
    limit?: string;
    page?: string;
  };
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseIndexListData>(
    "/admin/cloud.ClassroomCourse/index",
    {
      method: "GET",
      params: query,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseCreateCourseCreate
 * @summary 创建课程
 * @request POST:/admin/cloud.ClassroomCourse/createCourse */
export async function cloudClassroomCourseCreateCourseCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseCreateCourseCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseCreateCourseCreateData>(
    "/admin/cloud.ClassroomCourse/createCourse",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseUpdateCourseCreate
 * @summary 编辑课程
 * @request POST:/admin/cloud.ClassroomCourse/updateCourse */
export async function cloudClassroomCourseUpdateCourseCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseUpdateCourseCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseUpdateCourseCreateData>(
    "/admin/cloud.ClassroomCourse/updateCourse",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseChangeIsHotCreate
 * @summary 热门状态修改
 * @request POST:/admin/cloud.ClassroomCourse/changeIsHot */
export async function cloudClassroomCourseChangeIsHotCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseChangeIsHotCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseChangeIsHotCreateData>(
    "/admin/cloud.ClassroomCourse/changeIsHot",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseChangeIsTopCreate
 * @summary 置顶状态修改
 * @request POST:/admin/cloud.ClassroomCourse/changeIsTop */
export async function cloudClassroomCourseChangeIsTopCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseChangeIsTopCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseChangeIsTopCreateData>(
    "/admin/cloud.ClassroomCourse/changeIsTop",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseChangeIsOffCreate
 * @summary 下架状态修改
 * @request POST:/admin/cloud.ClassroomCourse/changeIsOff */
export async function cloudClassroomCourseChangeIsOffCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseChangeIsOffCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseChangeIsOffCreateData>(
    "/admin/cloud.ClassroomCourse/changeIsOff",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseChangeStatusCreate
 * @summary 启用状态修改
 * @request POST:/admin/cloud.ClassroomCourse/changeStatus */
export async function cloudClassroomCourseChangeStatusCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseChangeStatusCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseChangeStatusCreateData>(
    "/admin/cloud.ClassroomCourse/changeStatus",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCategoryChangeCategoryStatusCreate
 * @summary 分类状态修改
 * @request POST:/admin/cloud.ClassroomCategory/changeCategoryStatus */
export async function cloudClassroomCategoryChangeCategoryStatusCreate({
  body,
  options,
}: {
  body: CloudClassroomCategoryChangeCategoryStatusCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCategoryChangeCategoryStatusCreateData>(
    "/admin/cloud.ClassroomCategory/changeCategoryStatus",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCategoryChangeCategorySortCreate
 * @summary 分类排序修改
 * @request POST:/admin/cloud.ClassroomCategory/changeCategorySort */
export async function cloudClassroomCategoryChangeCategorySortCreate({
  body,
  options,
}: {
  body: CloudClassroomCategoryChangeCategorySortCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCategoryChangeCategorySortCreateData>(
    "/admin/cloud.ClassroomCategory/changeCategorySort",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseDelCourseCreate
 * @summary 删除课程
 * @request POST:/admin/cloud.ClassroomCourse/delCourse */
export async function cloudClassroomCourseDelCourseCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseDelCourseCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseDelCourseCreateData>(
    "/admin/cloud.ClassroomCourse/delCourse",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
/**
 * No description *
 * @tags 云课堂
 * @name CloudClassroomCourseChangeSortCreate
 * @summary 课程排序修改
 * @request POST:/admin/cloud.ClassroomCourse/changeSort */
export async function cloudClassroomCourseChangeSortCreate({
  body,
  options,
}: {
  body: CloudClassroomCourseChangeSortCreatePayload;
  options?: CustomRequestOptions;
}) {
  return request<CloudClassroomCourseChangeSortCreateData>(
    "/admin/cloud.ClassroomCourse/changeSort",
    {
      method: "POST",

      data: body,
      ...(options || {}),
    },
  );
}
