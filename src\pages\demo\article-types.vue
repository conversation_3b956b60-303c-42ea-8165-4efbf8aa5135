<route lang="json5">
{
  style: {
    navigationBarTitleText: '文章类型演示',
  },
}
</route>

<template>
  <view class="demo-page">
    <view class="demo-section">
      <view class="section-title">文章类型演示</view>
      <view class="demo-content">
        <view class="type-buttons">
          <wd-button 
            type="primary" 
            @click="navigateToArticle('article')"
            class="type-btn"
          >
            普通文章
          </wd-button>
          
          <wd-button 
            type="success" 
            @click="navigateToArticle('gallery')"
            class="type-btn"
          >
            图片集
          </wd-button>
          
          <wd-button 
            type="warning" 
            @click="navigateToArticle('video')"
            class="type-btn"
          >
            视频课程
          </wd-button>
          
          <wd-button 
            type="info" 
            @click="navigateToArticle('document')"
            class="type-btn"
          >
            文档资料
          </wd-button>
          
          <wd-button 
            type="default" 
            @click="navigateToArticle('mixed')"
            class="type-btn"
          >
            混合内容
          </wd-button>
        </view>
      </view>
    </view>
    
    <view class="demo-section">
      <view class="section-title">类型说明</view>
      <view class="demo-content">
        <view class="type-descriptions">
          <view class="desc-item">
            <view class="desc-title">普通文章 (article)</view>
            <text class="desc-text">包含富文本内容和文档附件的标准文章格式</text>
          </view>
          
          <view class="desc-item">
            <view class="desc-title">图片集 (gallery)</view>
            <text class="desc-text">以图片展示为主的内容，支持多图预览和缩放</text>
          </view>
          
          <view class="desc-item">
            <view class="desc-title">视频课程 (video)</view>
            <text class="desc-text">包含主视频和相关视频列表的课程内容</text>
          </view>
          
          <view class="desc-item">
            <view class="desc-title">文档资料 (document)</view>
            <text class="desc-text">以文档下载和预览为主的资料库内容</text>
          </view>
          
          <view class="desc-item">
            <view class="desc-title">混合内容 (mixed)</view>
            <text class="desc-text">包含文本、图片、视频、文档等多种类型的综合内容</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="demo-section">
      <view class="section-title">使用方法</view>
      <view class="demo-content">
        <view class="usage-info">
          <text class="usage-text">
            在跳转到文章页面时，通过 URL 参数传递 type 来指定文章类型：
          </text>
          <view class="code-example">
            <text class="code-text">
              uni.navigateTo({
                url: '/pages/cloudClassRoom/article?id=123&type=gallery'
              })
            </text>
          </view>
          <text class="usage-text">
            支持的类型：article、gallery、video、document、mixed
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 跳转到文章页面
const navigateToArticle = (type: string) => {
  const articleId = `demo_${type}_${Date.now()}`
  
  uni.navigateTo({
    url: `/pages/cloudClassRoom/article?id=${articleId}&type=${type}`
  })
}
</script>

<style scoped lang="scss">
.demo-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
  
  .demo-section {
    background: white;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    
    .section-title {
      background: #42b981;
      color: white;
      font-size: 32rpx;
      font-weight: bold;
      padding: 20rpx 30rpx;
    }
    
    .demo-content {
      padding: 30rpx;
    }
  }
  
  .type-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    
    .type-btn {
      height: 80rpx;
      font-size: 28rpx;
    }
  }
  
  .type-descriptions {
    .desc-item {
      margin-bottom: 25rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .desc-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      .desc-text {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
  
  .usage-info {
    .usage-text {
      display: block;
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      margin-bottom: 20rpx;
    }
    
    .code-example {
      background: #f1f3f4;
      border-radius: 8rpx;
      padding: 20rpx;
      margin: 20rpx 0;
      
      .code-text {
        font-family: 'Courier New', monospace;
        font-size: 24rpx;
        color: #333;
        line-height: 1.4;
      }
    }
  }
}
</style>
