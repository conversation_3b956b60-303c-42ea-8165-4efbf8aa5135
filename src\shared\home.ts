/**
 * 导航位置信息
 */
export interface NavigationLocation {
  longitude: string // 经度
  latitude: string // 纬度
  distance: number | null // 距离，可能为null
  address: string // 地址
  id: string // 唯一标识符
}

/**
 * 滚动图片链接信息
 */
export interface RollPictureLink {
  url: string // 图片链接URL
  name: string // 图片名称
  checked: string // 是否选中（'1'表示选中，'0'表示未选中）
  link: string // 附加链接，可能为空字符串
  uid: string // 唯一用户ID
}

/**
 * 滚动图片信息
 */
export interface RollPicture {
  url: string // 图片URL
  name: string // 图片名称
  checked: string // 是否选中（'1'表示选中，'0'表示未选中）
  link: RollPictureLink[] // 附加链接列表
  uid: string // 唯一用户ID
}

/**
 * 响应数据
 */
export interface Data {
  id: number // 医院ID
  hospital_id: number // 医院ID
  hospital_name: string // 医院名称
  hospital_address: string // 医院地址
  phone_number: string // 电话号码
  server_day_start: number // 服务开始日（例如：3代表周三）
  server_day_end: number // 服务结束日（例如：3代表周三）
  server_time_start: string // 服务开始时间（格式：HH:mm:ss）
  server_time_end: string // 服务结束时间（格式：HH:mm:ss）
  navigation_location: NavigationLocation[] // 导航位置列表
  header_picture: string // 头部图片数据（base64格式）
  roll_picture: RollPicture[] // 滚动图片列表
  create_time: number // 创建时间戳
  update_time: number // 更新时间戳
  detete_time: number // 删除时间戳 (注意：字段名可能是typo，应为 'delete_time')
  module: string[] // 模块列表 (字符串数组，元素是数字字符串)
}

/**
 * API响应的根类型
 */
export interface ApiResponse {
  code: number // 响应代码
  msg: string // 响应消息
  time: number // 响应时间戳
  data: Data // 具体数据内容
}

export enum Module {
  '预约挂号' = 1,
  'AI问诊' = 2,
  '预约信息' = 3,
  '病历信息' = 4,
  '检查信息' = 5,
  '检验信息' = 6,
  '处方信息' = 7,
  '治疗信息' = 8,
  '随访记录' = 9,
  '电子账单' = 10,
}
