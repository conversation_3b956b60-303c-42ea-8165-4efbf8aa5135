/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface SurveySurveyInfoGetSurveyViewCreatePayload {
  /**
   * 数据ID
   * @example 0
   */
  id?: number
}

export type SurveySurveyInfoGetSurveyViewCreateData = object

export interface SurveySurveyInfoGetSurveyInfoCreatePayload {
  /**
   * 医院id
   * @example 0
   */
  hospital_id?: number
  /**
   * 病人姓名
   * @example ""
   */
  user_name?: string
  /**
   * 病案号
   * @example ""
   */
  patient_no?: string
  /**
   * 开始时间
   * @example ""
   */
  start_date?: string
  /**
   * 截止时间
   * @example ""
   */
  end_date?: string
  /**
   * 分页条数
   * @example ""
   */
  limit?: string
  /**
   * 分页页数
   * @example ""
   */
  page?: string
  /**
   * 诊室id
   * @example 0
   */
  surgery_id?: number
}

export type SurveySurveyInfoGetSurveyInfoCreateData = object

export interface SurveySurveyInfoCreateSurveyInfoCreatePayload {
  /**
   * 病人姓名
   * @example ""
   */
  user_name?: string
  /**
   * 手机号
   * @example 0
   */
  phone?: number
  /**
   * 性别
   * @example ""
   */
  sex?: string
  /**
   * 年龄
   * @example 0
   */
  age?: number
  /**
   * 病案号
   * @example ""
   */
  patient_no?: string
  /**
   * 答题数据
   * @example ""
   */
  survey_info?: string
  /**
   * 医院id
   * @example 0
   */
  hospital_id?: number
  /**
   * 阳性问题数
   * @example 0
   */
  issue_number?: number
  /**
   * 诊室id
   * @example 0
   */
  surgery_id?: number
}

export type SurveySurveyInfoCreateSurveyInfoCreateData = object

export type SurveySurveyLoginGetHospitalListCreateData = object

export interface SurveySurveyLoginHospitalLoginCreatePayload {
  /**
   * 医院id
   * @example 0
   */
  id?: number
  /**
   * 密码
   * @example ""
   */
  password?: string
}

export type SurveySurveyLoginHospitalLoginCreateData = object

export interface SurveySurveyInfoUpdateSurveyInfoCreatePayload {
  /**
   * 数据ID
   * @example 0
   */
  id?: number
}

export type SurveySurveyInfoUpdateSurveyInfoCreateData = object

export interface SurveySurveyLoginGetSurgeryListCreatePayload {
  /**
   * 医院id
   * @example 0
   */
  hospital_id?: number
}

export type SurveySurveyLoginGetSurgeryListCreateData = object

export interface SurveySurveyLoginUpdateHospitalPwdCreatePayload {
  /**
   * 医院id
   * @example ""
   */
  id?: string
  /**
   * 新密码
   * @example ""
   */
  pwd?: string
}

export type SurveySurveyLoginUpdateHospitalPwdCreateData = object

export interface SurveySurveyLoginUpdateSurgeryPwdCreatePayload {
  /**
   * 诊室id
   * @example 0
   */
  id?: number
  /**
   * 新密码
   * @example ""
   */
  pwd?: string
}

export type SurveySurveyLoginUpdateSurgeryPwdCreateData = object

export interface SurveyWatchWatchListCreatePayload {
  /**
   * 状态 1空闲 2使用中
   * @example ""
   */
  status?: string
  /**
   * 使用人
   * @example ""
   */
  user_id?: string
}

export type SurveyWatchWatchListCreateData = object

export interface SurveyWatchUpdateWatchThresholdCreatePayload {
  /**
   * 设备编号
   * @example ""
   */
  device_code?: string
  /**
   * 体温最大阈值
   * @example ""
   */
  tp_max?: string
  /**
   * 体温最小阈值
   * @example ""
   */
  tp_min?: string
  /**
   * 心率最大阈值
   * @example ""
   */
  hr_max?: string
  /**
   * 心率最小阈值
   * @example ""
   */
  hr_min?: string
  /**
   * 血氧最小阈值
   * @example ""
   */
  spo2_min?: string
  /**
   * 舒张压最大
   * @example ""
   */
  dbp_max?: string
  /**
   * 舒张压最小
   * @example ""
   */
  dbp_min?: string
  /**
   * 收缩压最大
   * @example ""
   */
  sbp_max?: string
  /**
   * 收缩压最小
   * @example ""
   */
  sbp_min?: string
}

export type SurveyWatchUpdateWatchThresholdCreateData = object

export interface SurveyWatchBindUserCreatePayload {
  /**
   * 设备编号
   * @example ""
   */
  device_code?: string
  /**
   * 用户id
   * @example ""
   */
  user_id?: string
}

export type SurveyWatchBindUserCreateData = object

export interface SurveyWatchUnbindUserCreatePayload {
  /**
   * 设备编号
   * @example ""
   */
  device_code?: string
  /**
   * 用户id
   * @example ""
   */
  user_id?: string
}

export type SurveyWatchUnbindUserCreateData = object

export interface SurveyWatchCreateUserCreatePayload {
  /**
   * 用户id
   * @example ""
   */
  user_id?: string
  /**
   * 用户名
   * @example ""
   */
  user_name?: string
  /**
   * 手机号
   * @example ""
   */
  phone?: string
  /**
   * 身份证号
   * @example ""
   */
  id_card?: string
}

export type SurveyWatchCreateUserCreateData = object

export interface SurveyWatchGetUserListCreatePayload {
  /** @example "" */
  filter?: string
  /**
   * 状态 1未绑定 2已绑定
   * @example ""
   */
  status?: string
  /**
   * 设备编号
   * @example ""
   */
  device_code?: string
}

export type SurveyWatchGetUserListCreateData = object

export interface SurveyWatchGetUserInfoCreatePayload {
  /** @example "" */
  user_id?: string
}

export type SurveyWatchGetUserInfoCreateData = object

export interface SurveyWatchGetEarlyInfoCreatePayload {
  /** @example "" */
  user_id?: string
}

export type SurveyWatchGetEarlyInfoCreateData = object

export interface SurveyWatchBindUserByAppCreatePayload {
  /**
   * 用户id
   * @example ""
   */
  user_id?: string
  /**
   * 设备编号
   * @example ""
   */
  device_code?: string
}

export type SurveyWatchBindUserByAppCreateData = object

export interface SurveyWatchBindWacthCreatePayload {
  /**
   * 设备编号
   * @example ""
   */
  device_code?: string
}

export type SurveyWatchBindWacthCreateData = object

export interface SurveyWatchUpdateFallDownCreatePayload {
  /**
   * 跌倒提醒数据id (fall_down)
   * @example ""
   */
  id?: string
}

export type SurveyWatchUpdateFallDownCreateData = object

export interface SurveyWatchDelWacthCreatePayload {
  /**
   * 返回列表数据id
   * @example ""
   */
  id?: string
}

export type SurveyWatchDelWacthCreateData = object

export interface SurveyWatchGetUserAllInfoCreatePayload {
  /**
   * 用户id
   * @example ""
   */
  user_id?: string
  /**
   * 开始时间
   * @example ""
   */
  start_date?: string
  /**
   * 结束时间
   * @example ""
   */
  end_date?: string
}

export type SurveyWatchGetUserAllInfoCreateData = object

export interface SurveyWatchReportListCreatePayload {
  /**
   * 用户id
   * @example ""
   */
  user_id?: string
}

export type SurveyWatchReportListCreateData = object

export interface SurveyWatchReportDetailCreatePayload {
  /** @example "" */
  id?: string
}

export type SurveyWatchReportDetailCreateData = object

export interface SurveyWatchGetUserHistoryInfoCreatePayload {
  /** @example "" */
  user_id?: string
  /** @example "" */
  start_date?: string
  /** @example "" */
  end_date?: string
}

export type SurveyWatchGetUserHistoryInfoCreateData = object

export interface SurveyWatchGetEarlyHistoryInfoCreatePayload {
  /** @example "" */
  user_id?: string
  /** @example "" */
  start_date?: string
  /** @example "" */
  end_date?: string
}

export type SurveyWatchGetEarlyHistoryInfoCreateData = object
