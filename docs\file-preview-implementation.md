# 微信小程序文件预览功能实现文档

## 概述

本项目为微信小程序实现了完整的多媒体文件预览功能，支持图片、视频、文档（Word、Excel、PDF）和富文本的预览。同时根据内容类型提供了不同的展示模式。

## 功能特性

### 1. 文件预览功能

#### 图片预览
- ✅ 单图预览：支持缩放、保存到相册
- ✅ 多图预览：支持左右滑动浏览
- ✅ 网格展示：支持自定义网格列数
- ✅ 缩略图支持：提升加载性能

#### 视频预览
- ✅ 在线播放：支持常见视频格式
- ✅ 自定义控制：播放、暂停、全屏
- ✅ 封面图显示：提升用户体验
- ✅ 下载保存：支持保存到相册

#### 文档预览
- ✅ PDF预览：使用微信原生 `uni.openDocument()` API
- ✅ Office文档：支持在线预览服务（Microsoft Office Online）
- ✅ 文档下载：支持本地下载
- ✅ 文件信息：显示文件大小、类型等

#### 富文本预览
- ✅ HTML渲染：支持基本HTML标签
- ✅ 安全过滤：移除危险标签和脚本
- ✅ 内容复制：支持文本复制到剪贴板
- ✅ 内容分享：支持微信分享

### 2. 文件上传功能

#### 多类型上传
- ✅ 图片上传：支持相册和拍照
- ✅ 视频上传：支持本地视频选择
- ✅ 文档上传：支持从聊天记录选择文件
- ✅ 批量上传：支持同时上传多个文件

#### 上传限制
- ✅ 文件大小限制：可配置最大文件大小
- ✅ 文件类型限制：可配置允许的文件类型
- ✅ 数量限制：可配置最大上传数量

### 3. 内容类型支持

#### 文章类型 (article)
- 富文本内容展示
- 文档附件列表
- 标准文章布局

#### 图片集 (gallery)
- 大图网格展示
- 图片描述信息
- 优化的图片浏览体验

#### 视频课程 (video)
- 主视频播放器
- 课程介绍信息
- 相关视频推荐

#### 文档资料 (document)
- 文档列表展示
- 预览和下载功能
- 文档类型图标

#### 混合内容 (mixed)
- 富文本 + 图片 + 视频 + 文档
- 综合内容展示
- 灵活的布局结构

## 技术实现

### 核心组件

#### 1. 文件预览组件 (`src/components/file-preview/`)
```
├── ImagePreview.vue      # 图片预览组件
├── VideoPreview.vue      # 视频预览组件
├── DocumentPreview.vue   # 文档预览组件
├── RichTextPreview.vue   # 富文本预览组件
└── index.ts             # 统一导出
```

#### 2. 预览功能钩子 (`src/hooks/useFilePreview.ts`)
- `getFileType()` - 文件类型识别
- `previewImages()` - 图片预览
- `previewVideo()` - 视频预览
- `previewDocument()` - 文档预览
- `previewRichText()` - 富文本预览
- `previewFile()` - 统一预览入口
- `previewFiles()` - 批量预览

#### 3. 文件上传钩子 (`src/hooks/useUpload.ts`)
- `useUpload()` - 原有图片上传功能
- `useFileUpload()` - 新增多类型文件上传功能

#### 4. 统一预览页面 (`src/pages/file-preview/index.vue`)
- 支持所有文件类型的预览
- 根据URL参数动态加载对应组件
- 统一的用户界面和交互

### 类型定义 (`src/typings.d.ts`)

```typescript
type FileType = 'image' | 'video' | 'document' | 'richtext'

type FileItem = {
  id?: string
  name: string
  url: string
  type: FileType
  size?: number
  extension?: string
  thumbnail?: string
}

type PreviewOptions = {
  current?: number
  files: FileItem[]
  showDownload?: boolean
  showShare?: boolean
}
```

## 使用方法

### 1. 基本预览

```typescript
import { previewFile } from '@/hooks/useFilePreview'

// 预览单个文件
const file: FileItem = {
  name: '示例图片.jpg',
  url: 'https://example.com/image.jpg',
  type: 'image'
}
previewFile(file)
```

### 2. 批量预览

```typescript
import { previewFiles } from '@/hooks/useFilePreview'

// 预览多个文件
const files: FileItem[] = [
  { name: '图片1.jpg', url: '...', type: 'image' },
  { name: '图片2.jpg', url: '...', type: 'image' }
]
previewFiles({ files, current: 0 })
```

### 3. 文件上传

```typescript
import { useFileUpload } from '@/hooks/useUpload'

const { 
  loading, 
  uploadedFiles, 
  chooseFileType 
} = useFileUpload({
  maxCount: 5,
  fileTypes: ['image', 'video', 'document'],
  maxSize: 10 // MB
})

// 选择文件
chooseFileType()
```

### 4. 内容类型展示

```typescript
// 跳转到文章页面并指定类型
uni.navigateTo({
  url: `/pages/cloudClassRoom/article?id=123&type=gallery`
})
```

## 演示页面

### 1. 文件预览演示 (`src/pages/demo/file-preview.vue`)
- 展示所有预览功能
- 测试文件上传
- 交互式演示

### 2. 文章类型演示 (`src/pages/demo/article-types.vue`)
- 展示不同内容类型
- 类型说明和使用方法
- 快速跳转测试

### 3. 测试页面 (`src/pages/test/preview.vue`)
- 功能测试入口
- 快速验证各项功能
- 开发调试工具

## 配置说明

### 文档预览服务

在 `DocumentPreview.vue` 中可以配置在线预览服务：

```typescript
// Microsoft Office Online (默认)
previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`

// Google Docs Viewer
previewUrl = `https://docs.google.com/gview?url=${encodedUrl}&embedded=true`

// 腾讯文档预览服务（需要申请）
previewUrl = `https://preview.cloud.tencent.com/preview?url=${encodedUrl}`
```

### 上传服务配置

在 `src/utils/index.ts` 中配置上传地址：

```typescript
export const getEnvBaseUploadUrl = () => {
  // 根据环境返回不同的上传地址
  return import.meta.env.VITE_UPLOAD_BASEURL
}
```

## 注意事项

1. **微信小程序限制**
   - 文档预览需要下载到本地
   - 视频播放有格式限制
   - 网络请求需要配置域名白名单

2. **性能优化**
   - 图片使用缩略图提升加载速度
   - 大文件下载前显示文件大小
   - 视频使用封面图减少流量消耗

3. **用户体验**
   - 提供加载状态提示
   - 错误处理和重试机制
   - 支持离线查看已下载文件

4. **安全考虑**
   - 富文本内容过滤危险标签
   - 文件类型验证
   - 文件大小限制

## 扩展建议

1. **功能扩展**
   - 支持音频文件预览
   - 添加文件收藏功能
   - 实现文件搜索功能

2. **性能优化**
   - 实现图片懒加载
   - 添加文件缓存机制
   - 优化大文件处理

3. **用户体验**
   - 添加手势操作支持
   - 实现全屏预览模式
   - 支持文件批量操作

## 总结

本实现提供了完整的文件预览解决方案，支持微信小程序中常见的文件类型，具有良好的扩展性和用户体验。通过模块化的设计，可以轻松集成到现有项目中，并根据需求进行定制化开发。
