<template>
  <view class="appointment-container">
    <!-- Department Selection -->
    <view class="section-title">选择科室</view>
    <view class="department-grid">
      <view
        v-for="(dept, index) in departments"
        :key="index"
        class="department-item"
        :class="{ active: selectedDepartment === dept.id }"
        @click="selectDepartment(dept.id)"
      >
        <view class="dept-icon">
          <wd-icon
            :name="dept.icon"
            size="24px"
            :color="selectedDepartment === dept.id ? '#ffffff' : '#666666'"
          />
        </view>
        <view class="dept-name">{{ dept.name }}</view>
      </view>
    </view>

    <!-- Date Selection -->
    <view class="section-title">选择日期</view>
    <scroll-view class="date-scroll" scroll-x>
      <view class="date-container">
        <view
          v-for="(date, index) in availableDates"
          :key="index"
          class="date-item"
          :class="{ active: selectedDate === date.value }"
          @click="selectDate(date.value)"
        >
          <view class="date-day">{{ date.day }}</view>
          <view class="date-weekday">{{ date.weekday }}</view>
          <view class="date-month">{{ date.month }}</view>
        </view>
      </view>
    </scroll-view>

    <!-- Time Slots -->
    <view class="section-title">选择时间段</view>
    <view class="time-container">
      <view class="time-period">
        <view class="period-title">上午</view>
        <view class="time-slots">
          <view
            v-for="(slot, index) in morningSlots"
            :key="index"
            class="time-slot"
            :class="{
              active: selectedTimeSlot === slot.id,
              disabled: !slot.available,
            }"
            @click="selectTimeSlot(slot)"
          >
            {{ slot.time }}
          </view>
        </view>
      </view>

      <view class="time-period">
        <view class="period-title">下午</view>
        <view class="time-slots">
          <view
            v-for="(slot, index) in afternoonSlots"
            :key="index"
            class="time-slot"
            :class="{
              active: selectedTimeSlot === slot.id,
              disabled: !slot.available,
            }"
            @click="selectTimeSlot(slot)"
          >
            {{ slot.time }}
          </view>
        </view>
      </view>
    </view>

    <!-- Doctor Selection -->
    <view class="section-title">选择医生</view>
    <view class="doctor-list">
      <view
        v-for="(doctor, index) in availableDoctors"
        :key="index"
        class="doctor-item"
        :class="{ active: selectedDoctor === doctor.id }"
        @click="selectDoctor(doctor.id)"
      >
        <image class="doctor-avatar" :src="doctor.avatar" mode="aspectFill" />
        <view class="doctor-info">
          <view class="doctor-name">{{ doctor.name }}</view>
          <view class="doctor-title">{{ doctor.title }}</view>
          <view class="doctor-specialty">{{ doctor.specialty }}</view>
        </view>
        <view class="doctor-rating">
          <wd-icon name="star-fill" size="14px" color="#FFB300" />
          <text class="rating-text">{{ doctor.rating }}</text>
        </view>
      </view>
    </view>

    <!-- Patient Information -->
    <view class="section-title">就诊人信息</view>
    <view class="patient-info">
      <view class="info-row">
        <view class="info-label">姓名</view>
        <view class="info-value">
          <input type="text" v-model="patientInfo.name" placeholder="请输入姓名" />
        </view>
      </view>
      <view class="info-row">
        <view class="info-label">性别</view>
        <view class="info-value gender-selection">
          <view
            class="gender-option"
            :class="{ active: patientInfo.gender === 'male' }"
            @click="patientInfo.gender = 'male'"
          >
            男
          </view>
          <view
            class="gender-option"
            :class="{ active: patientInfo.gender === 'female' }"
            @click="patientInfo.gender = 'female'"
          >
            女
          </view>
        </view>
      </view>
      <view class="info-row">
        <view class="info-label">手机号</view>
        <view class="info-value">
          <input type="number" v-model="patientInfo.phone" placeholder="请输入手机号" />
        </view>
      </view>
      <view class="info-row">
        <view class="info-label">身份证</view>
        <view class="info-value">
          <input type="text" v-model="patientInfo.idCard" placeholder="请输入身份证号" />
        </view>
      </view>
    </view>

    <!-- Submit Button -->
    <view class="submit-container">
      <wd-button block type="primary" class="submit-btn" @click="submitAppointment">
        确认预约
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'

// Department selection
const departments = reactive([
  { id: 1, name: '内科', icon: 'heart' },
  { id: 2, name: '外科', icon: 'scissors' },
  { id: 3, name: '儿科', icon: 'users' },
  { id: 4, name: '妇产科', icon: 'user-plus' },
  { id: 5, name: '眼科', icon: 'eye' },
  { id: 6, name: '口腔科', icon: 'smile' },
  { id: 7, name: '皮肤科', icon: 'droplet' },
  { id: 8, name: '精神科', icon: 'brain' },
])
const selectedDepartment = ref(1)

// Date selection
const today = new Date()
const availableDates = reactive(
  Array.from({ length: 14 }, (_, i) => {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return {
      value: date.toISOString().split('T')[0],
      day: date.getDate(),
      weekday: weekdays[date.getDay()],
      month: `${date.getMonth() + 1}月`,
    }
  }),
)
const selectedDate = ref(availableDates[0].value)

// Time slots
const morningSlots = reactive([
  { id: 1, time: '08:00', available: true },
  { id: 2, time: '08:30', available: true },
  { id: 3, time: '09:00', available: false },
  { id: 4, time: '09:30', available: true },
  { id: 5, time: '10:00', available: true },
  { id: 6, time: '10:30', available: false },
  { id: 7, time: '11:00', available: true },
  { id: 8, time: '11:30', available: true },
])

const afternoonSlots = reactive([
  { id: 9, time: '13:30', available: true },
  { id: 10, time: '14:00', available: false },
  { id: 11, time: '14:30', available: true },
  { id: 12, time: '15:00', available: true },
  { id: 13, time: '15:30', available: false },
  { id: 14, time: '16:00', available: true },
  { id: 15, time: '16:30', available: true },
  { id: 16, time: '17:00', available: true },
])

const selectedTimeSlot = ref(null)

// Doctor selection
const availableDoctors = reactive([
  {
    id: 1,
    name: '张医生',
    title: '主任医师',
    specialty: '内科 - 消化系统',
    rating: 4.9,
    avatar: '/static/doctor-1.jpg',
  },
  {
    id: 2,
    name: '李医生',
    title: '副主任医师',
    specialty: '内科 - 心血管',
    rating: 4.7,
    avatar: '/static/doctor-2.jpg',
  },
  {
    id: 3,
    name: '王医生',
    title: '主治医师',
    specialty: '内科 - 呼吸系统',
    rating: 4.5,
    avatar: '/static/doctor-3.jpg',
  },
])
const selectedDoctor = ref(null)

// Patient information
const patientInfo = reactive({
  name: '',
  gender: 'male',
  phone: '',
  idCard: '',
})

// Methods
const selectDepartment = (id: number) => {
  selectedDepartment.value = id
}

const selectDate = (date: string) => {
  selectedDate.value = date
}

const selectTimeSlot = (slot: any) => {
  if (slot.available) {
    selectedTimeSlot.value = slot.id
  }
}

const selectDoctor = (id: number) => {
  selectedDoctor.value = id
}

const submitAppointment = () => {
  // Validate form
  if (!patientInfo.name) {
    showToast({
      title: '请输入姓名',
      icon: 'none',
    })
    return
  }

  if (!patientInfo.phone) {
    showToast({
      title: '请输入手机号',
      icon: 'none',
    })
    return
  }

  if (!patientInfo.idCard) {
    showToast({
      title: '请输入身份证号',
      icon: 'none',
    })
    return
  }

  if (!selectedTimeSlot.value) {
    showToast({
      title: '请选择就诊时间',
      icon: 'none',
    })
    return
  }

  if (!selectedDoctor.value) {
    showToast({
      title: '请选择医生',
      icon: 'none',
    })
    return
  }

  // Submit appointment
  showLoading({
    title: '预约中...',
  })

  // Simulate API call
  setTimeout(() => {
    hideLoading()
    showModal({
      title: '预约成功',
      content: '您的预约已成功提交，请按时前往医院就诊',
      showCancel: false,
      success: () => {
        navigateTo({
          url: '/pages/appointment/success',
        })
      },
    })
  }, 1500)
}
</script>

<style scoped>
.appointment-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
  padding-bottom: 80px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 15px 0 10px;
}

.department-grid {
  display: flex;
  flex-wrap: wrap;
  background-color: white;
  border-radius: 10px;
  padding: 10px;
}

.department-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

.department-item.active .dept-icon {
  background-color: #2196f3;
}

.department-item.active .dept-name {
  color: #2196f3;
  font-weight: bold;
}

.dept-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.dept-name {
  font-size: 12px;
  color: #666;
}

.date-scroll {
  background-color: white;
  border-radius: 10px;
  padding: 10px 0;
}

.date-container {
  display: flex;
  padding: 0 10px;
}

.date-item {
  min-width: 60px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  border-radius: 5px;
  background-color: #f5f5f5;
}

.date-item.active {
  background-color: #2196f3;
}

.date-item.active .date-day,
.date-item.active .date-weekday,
.date-item.active .date-month {
  color: white;
}

.date-day {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.date-weekday {
  font-size: 14px;
  color: #666;
  margin: 2px 0;
}

.date-month {
  font-size: 12px;
  color: #999;
}

.time-container {
  background-color: white;
  border-radius: 10px;
  padding: 15px;
}

.time-period {
  margin-bottom: 15px;
}

.time-period:last-child {
  margin-bottom: 0;
}

.period-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.time-slots {
  display: flex;
  flex-wrap: wrap;
}

.time-slot {
  width: calc(25% - 10px);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 5px;
  margin: 0 5px 10px;
  font-size: 14px;
  color: #333;
}

.time-slot.active {
  background-color: #2196f3;
  color: white;
}

.time-slot.disabled {
  background-color: #e0e0e0;
  color: #999;
  text-decoration: line-through;
}

.doctor-list {
  background-color: white;
  border-radius: 10px;
  padding: 10px;
}

.doctor-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.doctor-item:last-child {
  border-bottom: none;
}

.doctor-item.active {
  background-color: #e3f2fd;
  border-radius: 5px;
}

.doctor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.doctor-info {
  flex: 1;
  margin-left: 15px;
}

.doctor-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.doctor-title {
  font-size: 14px;
  color: #666;
  margin: 3px 0;
}

.doctor-specialty {
  font-size: 12px;
  color: #999;
}

.doctor-rating {
  display: flex;
  align-items: center;
}

.rating-text {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}

.patient-info {
  background-color: white;
  border-radius: 10px;
  padding: 15px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.info-label {
  width: 70px;
  font-size: 14px;
  color: #666;
}

.info-value {
  flex: 1;
}

.info-value input {
  width: 100%;
  height: 36px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 0 10px;
  font-size: 14px;
}

.gender-selection {
  display: flex;
}

.gender-option {
  flex: 1;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 5px;
  margin-right: 10px;
  font-size: 14px;
  color: #333;
}

.gender-option:last-child {
  margin-right: 0;
}

.gender-option.active {
  background-color: #2196f3;
  color: white;
}

.submit-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  padding: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.submit-btn {
  background-color: #2196f3;
  color: white;
  height: 44px;
  font-size: 16px;
  border-radius: 22px;
}
</style>
