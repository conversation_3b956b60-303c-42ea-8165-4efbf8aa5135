<template>
  <div class="health-card w-full" @click="handleClick">
    <div class="health-card__header px-3 pt-3" v-if="$slots.icon">
      <div class="health-card__icon">
        <slot name="icon"></slot>
      </div>
      <div class="health-card__title">
        <div class="health-card__name">
          <slot name="title"></slot>
        </div>
      </div>
      <div class="health-card__actions bg-gray-100 text-gray-500 rounded-lg px-3">
        <slot name="actions">--</slot>
      </div>
    </div>
    <div class="health-card__value px-3 font-bold text-lg">
      <slot name="value"></slot>
    </div>
    <slot name="unit"></slot>
    <div class="health-card__content pb-3 px-1">
      <slot name="content"></slot>
    </div>
    <slot name="footer"></slot>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['click'])
const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.health-card {
  min-height: 200rpx;
  background-color: #fff;
  border-radius: 12px;

  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    margin-right: 8px;
    border-radius: 50%;
  }
  &__title {
    flex: 1;
  }
  &__name {
    font-size: 24rpx;
    color: #666;
  }
  &__value {
    color: #333;
  }
  &__actions {
    font-size: 14px;
    color: #999;
  }
  &__content {
    height: 180rpx;

    min-height: 180rpx;
  }
}
</style>
