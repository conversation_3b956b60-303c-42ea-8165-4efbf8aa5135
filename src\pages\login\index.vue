<template>
  <view class="relative">
    <!-- <image src="../../static/<EMAIL>" class="w-100vw h-100vh" /> -->
    <view class="login-container min-h-screen px-30rpx py-40rpx flex flex-col items-center">
      <view class="flex flex-col items-center mt-60rpx">
        <image
          src="https://video.br-s.cn/wechat/result.png"
          class="w-200rpx h-200rpx rounded-full"
        />

        <view class="mt-30rpx text-center">
          <text class="text-xl font-bold text-gray-800">BR客户服务端</text>
        </view>
      </view>
      <wd-form :model="formData" :rules="rules" ref="formRef">
        <view class="mt-60rpx rounded-xl p-40rpx flex flex-col gap-5 w-80vw">
          <view class="text-gray-600 text-26rpx font-bold">手机号</view>
          <wd-input
            v-model="formData.phone"
            prop="phone"
            placeholder="请输入手机号"
            :maxlength="11"
            :rules="[{ required: true, message: '请正确的手机号', pattern: /^1[3456789]\d{9}$/ }]"
            class="w-full mb-30rpx input-bg"
          />
          <view class="text-gray-600 text-26rpx font-bold">验证码</view>
          <wd-input
            prop="verificationCode"
            v-model="formData.verificationCode"
            placeholder="请输入验证码"
            :rules="[{ required: true, message: '请输入验证码' }]"
            class="w-full input-bg"
          >
            <template #suffix>
              <wd-button
                size="small"
                class="text-white px-20rpx h-60rpx rounded-lg"
                :disabled="isCodeDisabled"
                @click="getVerificationCode"
                custom-class="get-code-btn"
              >
                {{ captchaText }}
              </wd-button>
            </template>
          </wd-input>

          <wd-button
            block
            custom-class="login-btn"
            size="large"
            class="text-white h-90rpx text-36rpx rounded-full mt-50rpx"
            @click="handleLogin"
          >
            登录
          </wd-button>
        </view>

        <view
          class="fixed bottom-safe left-0 right-0 mb-60rpx text-center text-gray-600 text-26rpx"
        >
          <view class="flex items-center justify-center">
            <wd-checkbox v-model="formData.agreeTerms" class="text-blue-500" prop="agreeTerms">
              <text class="text-gray-600">请阅读并同意</text>
              <text class="text-blue-500">《隐私政策》《服务协议》</text>
            </wd-checkbox>
          </view>
        </view>
      </wd-form>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { indexLoginCreate } from '@/service/app'
import { ref } from 'vue'
import { useUserStore } from '@/store'
import { sendSmsCode } from '@/service/index/system'

const userStore = useUserStore()
const count = ref(60)
const captchaText = ref('获取验证码')
const timer = ref(null)
const formRef = ref(null)
const formData = ref({
  phone: '',
  verificationCode: '',
  agreeTerms: true,
})
const isCodeDisabled = computed(
  () =>
    !formData.value.phone || count.value < 60 || !/^1[3456789]\d{9}$/.test(formData.value.phone),
)
const rules = ref({
  agreeTerms: [{ required: true, message: '请阅读并同意隐私政策和服务协议' }],
})
const code_id = ref('')
const getVerificationCode = () => {
  if (!formData.value.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
    })
    return
  }
  if (!/^1[3456789]\d{9}$/.test(formData.value.phone)) {
    uni.showToast({
      title: '手机号格式错误',
      icon: 'none',
    })
    return
  }
  uni.showLoading({ title: '发送验证码中...' })
  timer.value = setInterval(() => {
    captchaText.value = '重新获取(' + count.value + 's)'
    count.value--
    if (count.value < 0) {
      clearInterval(timer.value)
      captchaText.value = '获取验证码'
      count.value = 60
    }
  }, 1000)
  sendSmsCode(formData.value.phone)
    .then((res) => {
      code_id.value = res.data.code_id
      uni.hideLoading()
      uni.showToast({
        title: '发送验证码成功',
        icon: 'success',
      })
    })
    .catch((err) => {
      uni.hideLoading()
      uni.showToast({
        title: err.msg || '发送验证码失败',
        icon: 'none',
      })
      clearInterval(timer.value)
      count.value = 60
      captchaText.value = '获取验证码'
    })
}

const handleLogin = () => {
  // 验证表单
  formRef.value.validate().then(({ valid, errors }) => {
    if (!valid) {
      uni.showToast({
        title: errors[0].message,
        icon: 'none',
      })
      return
    }
    uni.showLoading({ title: '登录中...' })

    // 使用store中的login函数
    userStore
      .login(code_id.value, formData.value.verificationCode, formData.value.phone)
      .then((res) => {
        uni.hideLoading()
        uni.showToast({
          title: '登录成功',
          icon: 'success',
        })
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1500)
      })
      .catch((err) => {
        uni.hideLoading()
        uni.showToast({
          title: err.msg || '登录失败',
          icon: 'none',
        })
      })
  })
}
onBeforeUnmount(() => {
  clearInterval(timer.value)
})
</script>

<style scoped lang="scss">
.login-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  background: linear-gradient(135deg, #f5f7fa 0%, #e0f4ea 100%);
}

:deep(.get-code-btn) {
  color: white !important;
  background: #43b981 !important;
  border: none !important;
}

:deep(.login-btn) {
  background: linear-gradient(to right, #3ba975, #43b981) !important;
  border: none !important;
  box-shadow: 0 4rpx 10rpx rgba(67, 185, 129, 0.3) !important;
}

:deep(.wd-input) {
  // border-radius: 20rpx !important;
  padding: 10rpx !important;
  background: transparent !important;
  border-bottom: 1px solid #ccc !important;
}

.input-bg {
  background-color: white;
  border-radius: 8rpx;
}

.text-primary {
  color: #43b981;
}

:deep(.wd-checkbox__shape) {
  color: #43b981 !important;
  border-color: #43b981 !important;
}
</style>
