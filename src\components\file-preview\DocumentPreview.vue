<template>
  <view class="document-preview">
    <!-- PDF 预览 -->
    <view v-if="documentType === 'pdf'" class="pdf-preview">
      <view class="document-header">
        <view class="document-title">{{ documentFile.name }}</view>
        <view class="document-actions">
          <wd-button type="primary" size="small" @click="openDocument">
            打开文档
          </wd-button>
          <wd-button type="default" size="small" @click="downloadDocument">
            下载
          </wd-button>
        </view>
      </view>
      
      <view class="pdf-container">
        <view class="pdf-placeholder">
          <i class="i-carbon-document-pdf text-6xl text-red-500"></i>
          <text class="placeholder-text">PDF 文档</text>
          <text class="placeholder-desc">点击"打开文档"查看完整内容</text>
        </view>
      </view>
    </view>
    
    <!-- Office 文档预览 -->
    <view v-else-if="isOfficeDocument" class="office-preview">
      <view class="document-header">
        <view class="document-title">{{ documentFile.name }}</view>
        <view class="document-actions">
          <wd-button type="primary" size="small" @click="previewOnline">
            在线预览
          </wd-button>
          <wd-button type="default" size="small" @click="downloadDocument">
            下载
          </wd-button>
        </view>
      </view>
      
      <view class="office-container">
        <view class="office-placeholder">
          <i :class="getDocumentIcon()" class="text-6xl"></i>
          <text class="placeholder-text">{{ getDocumentTypeName() }}</text>
          <text class="placeholder-desc">点击"在线预览"查看文档内容</text>
        </view>
      </view>
    </view>
    
    <!-- WebView 预览模式 -->
    <view v-else-if="showWebView" class="webview-preview">
      <view class="webview-header">
        <wd-button type="default" size="small" @click="closeWebView">
          返回
        </wd-button>
        <view class="webview-title">{{ documentFile.name }}</view>
        <wd-button type="primary" size="small" @click="downloadDocument">
          下载
        </wd-button>
      </view>
      
      <web-view :src="previewUrl" class="webview-container" />
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <wd-loading type="circular" />
      <text class="loading-text">{{ loadingText }}</text>
    </view>
    
    <!-- 文档信息 -->
    <view v-if="showInfo" class="document-info">
      <view class="info-row">
        <text class="info-label">文件名：</text>
        <text class="info-value">{{ documentFile.name }}</text>
      </view>
      <view v-if="documentFile.size" class="info-row">
        <text class="info-label">文件大小：</text>
        <text class="info-value">{{ formatFileSize(documentFile.size) }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">文件类型：</text>
        <text class="info-value">{{ getDocumentTypeName() }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  documentFile: FileItem
  showInfo?: boolean
  previewService?: 'tencent' | 'microsoft' | 'google' | 'custom'
  customPreviewUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  showInfo: true,
  previewService: 'microsoft'
})

const loading = ref(false)
const loadingText = ref('')
const showWebView = ref(false)
const previewUrl = ref('')

// 文档类型
const documentType = computed(() => {
  const extension = props.documentFile.extension || 
    props.documentFile.url.split('.').pop()?.toLowerCase()
  console.log(extension)
  return extension as DocumentType
})

// 是否为 Office 文档
const isOfficeDocument = computed(() => {
  return ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(documentType.value)
})

// 获取文档图标
const getDocumentIcon = () => {
  const iconMap = {
    pdf: 'i-carbon-document-pdf text-red-500',
    doc: 'i-carbon-document text-blue-500',
    docx: 'i-carbon-document text-blue-500',
    xls: 'i-carbon-table text-green-500',
    xlsx: 'i-carbon-table text-green-500',
    ppt: 'i-carbon-presentation-file text-orange-500',
    pptx: 'i-carbon-presentation-file text-orange-500'
  }
  return iconMap[documentType.value] || 'i-carbon-document text-gray-500'
}

// 获取文档类型名称
const getDocumentTypeName = () => {
  const nameMap = {
    pdf: 'PDF 文档',
    doc: 'Word 文档',
    docx: 'Word 文档',
    xls: 'Excel 表格',
    xlsx: 'Excel 表格',
    ppt: 'PowerPoint 演示文稿',
    pptx: 'PowerPoint 演示文稿'
  }
  return nameMap[documentType.value] || '未知文档'
}

// 打开 PDF 文档
const openDocument = () => {
  loading.value = true
  loadingText.value = '正在下载文档...'
  
  uni.downloadFile({
    url: props.documentFile.url,
    success: (res) => {
      loading.value = false
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          fileType: documentType.value,
          success: () => {
            console.log('文档打开成功')
          },
          fail: (err) => {
            console.error('文档打开失败:', err)
            uni.showToast({
              title: '文档打开失败',
              icon: 'none'
            })
          }
        })
      }
    },
    fail: (err) => {
      loading.value = false
      console.error('文档下载失败:', err)
      uni.showToast({
        title: '文档下载失败',
        icon: 'none'
      })
    }
  })
}

// 在线预览
const previewOnline = () => {
  if (props.customPreviewUrl) {
    previewUrl.value = props.customPreviewUrl
  } else {
    // 使用在线预览服务
    const encodedUrl = encodeURIComponent(props.documentFile.url)
    
    switch (props.previewService) {
      case 'microsoft':
        previewUrl.value = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`
        break
      case 'google':
        previewUrl.value = `https://docs.google.com/gview?url=${encodedUrl}&embedded=true`
        break
      case 'tencent':
        // 腾讯文档预览服务（需要申请）
        previewUrl.value = `https://preview.cloud.tencent.com/preview?url=${encodedUrl}`
        break
      default:
        uni.showToast({
          title: '暂不支持在线预览',
          icon: 'none'
        })
        return
    }
  }
  
  showWebView.value = true
}

// 关闭 WebView
const closeWebView = () => {
  showWebView.value = false
  previewUrl.value = ''
}

// 下载文档
const downloadDocument = () => {
  loading.value = true
  loadingText.value = '正在下载文档...'
  
  uni.downloadFile({
    url: props.documentFile.url,
    success: (res) => {
      loading.value = false
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
        // 可以在这里添加保存到本地的逻辑
      }
    },
    fail: (err) => {
      loading.value = false
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  })
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}
</script>

<style scoped lang="scss">
.document-preview {
  .document-header,
  .webview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    
    .document-title,
    .webview-title {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin: 0 20rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .document-actions {
      display: flex;
      gap: 10rpx;
    }
  }
  
  .pdf-container,
  .office-container {
    min-height: 400rpx;
    border: 2rpx dashed #ddd;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .pdf-placeholder,
    .office-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20rpx;
      
      .placeholder-text {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .placeholder-desc {
        font-size: 28rpx;
        color: #666;
        text-align: center;
      }
    }
  }
  
  .webview-container {
    width: 100%;
    height: 80vh;
    border-radius: 8rpx;
    overflow: hidden;
  }
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
    
    .loading-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #666;
    }
  }
  
  .document-info {
    margin-top: 20rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    
    .info-row {
      display: flex;
      margin-bottom: 10rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        color: #666;
        font-size: 28rpx;
        min-width: 140rpx;
      }
      
      .info-value {
        color: #333;
        font-size: 28rpx;
        flex: 1;
      }
    }
  }
}
</style>
