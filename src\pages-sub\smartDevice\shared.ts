import dayjs from 'dayjs'

export enum HealthType {
  HEART_RATE = 'HEART_RATE',
  BLOOD_OXYGEN = 'BLOOD_OXYGEN',
  TEMPERATURE = 'TEMPERATURE',
  BLOOD_PRESSURE = 'BLOOD_PRESSURE',
  MOOD = 'MOOD',
  VITAL_INDEX = 'VITAL_INDEX',
  HEART_INDEX = 'HEART_INDEX',
}
/** 健康数据类型枚举 */
export enum HealthDataType {
  /** 焦虑 */
  ANXIETY = 'anxiety',
  /** 心律不齐 */
  SA = 'sa',
  /** 抑郁 */
  DEPRESSION = 'depression',
  /** 房颤 */
  AF = 'af',
  /** 血液黏稠 */
  BV = 'bv',
  /** 压力 */
  PRESSURE = 'pressure',
  /** 情绪 */
  EMO_RATIO = 'emoRatio',
  /** 呼吸率 */
  RR = 'rr',
  /** 离线 */
  OFFLINE = 'offline',
  /** 跌倒 */
  FALL_DOWN = 'fallDown',
  /** 疲劳 */
  FATIGUE = 'fatigue',
  /** 体温 */
  TP = 'tp',
  /** 心率 */
  HR = 'hr',
  /** 血氧 */
  SPO2 = 'spo2',
  /** 血压 */
  BP = 'bp',
}

export enum HealthTypeTitle {
  HEART_RATE = '心率',
  BLOOD_OXYGEN = '血氧',
  TEMPERATURE = '体温',
  BLOOD_PRESSURE = '血压',
  MOOD = '情绪',
  VITAL_INDEX = '生理指标',
  HEART_INDEX = '心理指标',
}
export const MoodType = ['']

// 建立健康类型和数据类型的映射关系
export const HealthTypeToDataType: Record<HealthType, HealthDataType> = {
  [HealthType.HEART_RATE]: HealthDataType.HR,
  [HealthType.BLOOD_OXYGEN]: HealthDataType.SPO2,
  [HealthType.TEMPERATURE]: HealthDataType.TP,
  [HealthType.BLOOD_PRESSURE]: HealthDataType.BP,
  [HealthType.MOOD]: HealthDataType.EMO_RATIO,
  [HealthType.VITAL_INDEX]: HealthDataType.PRESSURE,
  [HealthType.HEART_INDEX]: HealthDataType.ANXIETY,
}

export const getLast10Data = (data: any) => {
  const entries = Object.entries(data || {})

  const last10 = entries.length > 10 ? entries.slice(-10) : entries
  return {
    keys: last10.map((item) => item[0]),
    values: last10.map((item) => item[1]),
  }
}

export const formateDate = (date: string) => {
  console.log('formateDate', date)
  if (!date) {
    return '--'
  }
  return dayjs(date).isValid() ? dayjs(date).format('HH:mm') : '--'
}
export const sortLogData = (data: {}) => {
  console.log(data)
  const entries = Object.entries(data || {}).sort((a, b) => {
    return a[0].localeCompare(b[0]) // 按时间字符串自然排序
  })

  return {
    keys: entries.map((item) => item[0]),
    values: entries.map((item) => item[1]),
  }
}

export const getDataZoom = (data: any[] = []) => {
  if (!Array.isArray(data)) {
    return {
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: 0,
          zoomLock: true,
          moveOnMouseMove: true,
          preventDefaultMouseMove: false,
          zoomOnMouseWheel: false,
          startValue: 0,
          endValue: 0,
        },
      ],
    }
  }

  const length = data.length
  const startValue = length > 4 ? length - 4 : 0
  const endValue = length

  return {
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: 0,
        zoomLock: true,
        moveOnMouseMove: true,
        preventDefaultMouseMove: false,
        zoomOnMouseWheel: false,
        startValue,
        endValue,
      },
    ],
  }
}

export const getHealthLevel = (value: number) => {
  if (value >= 80) {
    return '重度'
  } else if (value >= 60) {
    return '中度'
  } else if (value >= 30) {
    return '轻度'
  } else if (value >= 0) {
    return '正常'
  }
  return '未知'
}

export const analyzeArray = (
  data: number[][],
  tab?: number,
  decimals = 1,
): {
  max: number
  min: number
  avg: number
  normalCount: number
  mildCount: number
  moderateCount: number
  severeCount: number
} => {
  // 将所有数字展平到一个数组并转换为百分比形式
  let flatArray: any[] = data
  //   console.log('flatArray.length', flatArray.length)
  // 计算最大值、最小值和平均值
  const max = Number(Math.max(...flatArray).toFixed(decimals))
  const min = Number(Math.min(...flatArray).toFixed(decimals))
  const avg = Math.floor(flatArray.reduce((sum, num) => sum + Number(num), 0) / flatArray.length)

  // 计算各个区间的次数
  const normalCount = flatArray.filter((num) => num >= 0 && num <= 29).length // 0-29 正常
  const mildCount = flatArray.filter((num) => num >= 30 && num <= 59).length // 30-59 轻度
  const moderateCount = flatArray.filter((num) => num >= 60 && num <= 79).length // 60-79 中度
  const severeCount = flatArray.filter((num) => num >= 80 && num <= 100).length // 80-100 重度

  return {
    max: isFinite(max) ? max : 0,
    min: isFinite(min) ? min : 0,
    avg: isFinite(avg) ? avg : 0,
    normalCount,
    mildCount,
    moderateCount,
    severeCount,
  }
}
