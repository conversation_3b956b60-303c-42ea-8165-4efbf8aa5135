<route lang="json5">
{
  style: {
    navigationBarTitleText: '云课堂',
  },
}
</route>
<template>
  <view class="min-h-screen bg-gray-100">
    <!-- Banner Section -->
    <view class="py-3 bg-white card-swiper">
      
      <wd-swiper
        autoplay
        custom-indicator-class="custom-indicator-class"
        custom-image-class="custom-image"
        custom-next-image-class="custom-image-prev"
        custom-prev-image-class="custom-image-prev"
        :list="topBanner"
        value-key="image"
        :indicator="{ type: 'dots-bar' }"
      />
    </view>

    <!-- Category Cards -->
    <scroll-view :scroll-x="true" :scroll-y="false" :show-scrollbar="false" class="my-3">
      <view class="flex gap-5 mx-3">
        <view
          v-for="(category, index) in hotList"
          :key="index"
          class="relative bg-white rounded-lg overflow-hidden flex-shrink-0 w-[300rpx]"
          @click="navigateToCategory(category.id)"
        >
          <image class="w-[300rpx] h-[70px]" :src="category.content && category.content[0].fullUrl" mode="aspectFill" />
          <!-- <view
            v-if="category.tag"
            class="absolute top-1.25 right-1.25 py-0.5 px-1 text-2xs text-white bg-orange-500 rounded-full"
          >
            {{ category.tag }}
          </view> -->
          <view class="text-sm text-xs py-2 px-2">{{ category.title }}</view>
          <view class="text-sm text-xs py-2 px-2">  {{ dayjs(category.create_time * 1000).format('YYYY-MM-DD') }}</view>

          <!-- <view class="flex justify-end justify-between pb-2 px-2 text-gray-600 text-xs">
            <view class="flex gap-1 items-center">
              <text>5.0</text>
              <wd-rate :modelValue="5" readonly size="12px" active-color="orange" />
            </view>
            <view class="flex items-center">
              <wd-icon name="view" size="12px" color="#999" />
              <text>123</text>
            </view>
          </view> -->
        </view>
      </view>
    </scroll-view>

    <!-- Tab Navigation -->
    <wd-tabs v-model="activeTabIndex" sticky @change="handleTabChange">
      <wd-tab
        v-for="(tab, index) in tabs"
        :key="index"
        :title="tab.title"
        :name="tab.id.toString()"
      />
    </wd-tabs>

    <!-- Search Bar -->
    <wd-search
      v-model="searchQuery"
      placeholder="搜索知识"
      class="mx-4 my-2.5"
      clearable
      @change="handleSearchChange"
      @clear="handleSearchClear"
      @search="handleSearch"
      hide-cancel
    />

    <!-- Article List -->
    <view class="px-3 py-3" id="pagingContainer">
      <z-paging
        ref="paging"
        v-model="articles"
        @query="queryList"
        :paging-style="{
          top: topVal + 'px',
        }"
        auto-show-system-loading
      >
        <view
          v-for="(article, index) in articles"
          :key="index"
          class="bg-white rounded-lg overflow-hidden shadow-sm flex"
          @click="navigateToArticle(article.id)"
        >
          <view class="flex p-3 flex-1">
            <image
              class="w-[90px] h-[60px] rounded-md object-cover"
              :src="article.content && article.content[0].fullUrl"
              mode="aspectFill"
            />
            <view class="flex-1 pl-3 flex flex-col justify-between">
              <view class="flex items-center gap-1">
                <view class="text-sm text-gray-800 line-clamp-2">
                  {{ article.title }}
                </view>
                <!-- <view
                  v-if="article.is_top"
                  class="px-1 py-0.5 bg-primary text-white text-2xs rounded"
                >
                  置顶
                </view>
                <view
                  v-if="article.is_hot"
                  class="px-1 py-0.5 bg-orange-500 text-white text-2xs rounded"
                >
                  热门
                </view> -->
              </view>
              <!-- <view class="text-xs text-gray-500 mt-1 line-clamp-1">{{ article.content }}</view> -->
              <view class="flex items-center justify-between text-xs text-gray-500 mt-1">
                <view class="text-primary">{{ article.category_name }}</view>
                <text class="text-xs">
                  {{ dayjs(article.create_time * 1000).format('YYYY-MM-DD') }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { cloudClassroomCategoryIndexList, cloudClassroomCourseIndexList } from '@/service/app'
import { Course, CourseCategory } from '@/shared/couse'
import { getCurrentInstance } from 'vue'
import dayjs from 'dayjs'
import { debounce } from 'lodash'
const instance = getCurrentInstance()

const getNodeInfo = () => {
  const query = uni.createSelectorQuery().in(instance)
  query
    .select('#pagingContainer') // 获取界面元素，也可以传id
    .boundingClientRect((data) => {
      const nodeInfo: UniApp.NodeInfo = data as UniApp.NodeInfo
      console.log(data)
      topVal.value = uni.getSystemInfoSync().windowHeight - nodeInfo.top + 10
      console.log(topVal.value)
    })
    .exec()
}
const activeTabIndex = ref(0)
const searchQuery = ref('')

const topBanner = ref([])
const hotList = ref([])
const tabs = ref<CourseCategory[]>([])
const categories = ref([])

const articles = ref([])

const navigateToCategory = (id: number) => {
  uni.navigateTo({
    url: `/pages/cloudClassRoom/category?id=${id}`,
  })
}

const navigateToArticle = (id: number) => {
  uni.navigateTo({
    url: `/pages/cloudClassRoom/article?id=${id}`,
  })
}

const getData = () => {
  cloudClassroomCourseIndexList({
    query: {
      is_top: '1',
      page: '1',
      limit: '10',
    },
  }).then((res) => {
    topBanner.value = res.data.list.map((item: any) => item.content[0].fullUrl)
  })
  cloudClassroomCourseIndexList({
    query: {
      is_hot: '1',
      page: '1',
      limit: '10',
    },
  }).then((res) => {
    hotList.value = res.data.list
  })
  cloudClassroomCategoryIndexList({}).then((res) => {
    tabs.value = [
      {
        id: '',
        name: '全部',
        title: '全部',
        sort: 0,
        status: 0,
        create_time: 0,
        update_time: 0,
      },
      ...(res.data.list as CourseCategory[]),
    ]
  })
}

const paging = ref()
const queryList = (page: number) => {
  cloudClassroomCourseIndexList({
    query: {
      page: page.toString(),
      limit: '10',
      category_id: activeTabIndex.value.toString(),
    },
  }).then((res) => {
    paging.value.complete(res.data.list as Course[])
  })
}
const isFirstLoad = ref(true)
const topVal = ref(0)
onLoad(() => {
  getData()
})
onMounted(() => {
  nextTick(() => {
    getNodeInfo()
  })
})
onShow(() => {
  if (!isFirstLoad.value) {
    getData()
  }
  isFirstLoad.value = false
})
const onSwiperChange = (current: number, source: string) => {
  console.log('轮播切换', current, source)
}

const handleSwiperClick = (index: number, item: any) => {
  console.log('轮播点击', index, item)
  uni.navigateTo({
    url: `/pages/cloudClassRoom/article?id=${item.id}`,
  })
}
const handleTabChange = (index: number) => {
  paging.value.reload()
}
const debounceReload = debounce(() => {
  paging.value.reload()
}, 300)

const handleSearchChange = (value: string) => {
  if (value) {
    debounceReload()
  }
}

const handleSearchClear = () => {
  debounceReload()
}

const handleSearch = (value: string) => {
  if (value) {
    debounceReload()
  }
}
</script>

<style scoped lang="scss">
/* Tailwind classes handle most styling */
/* Custom color classes for project colors */
.bg-primary {
  background-color: #42b981;
}

.text-primary {
  color: #42b981;
}

.bg-secondary {
  background-color: #4a9ff6;
}

.text-secondary {
  color: #4a9ff6;
}

.text-2xs {
  font-size: 10px;
}
.card-swiper {
  --wot-swiper-radius: 0;
  --wot-swiper-item-padding: 0 24rpx;
  // --wot-swiper-nav-dot-color: #e7e7e7;
  // --wot-swiper-nav-dot-active-color: #4d80f0;
  padding-bottom: 24rpx;

  :deep(.custom-image) {
    border-radius: 12rpx;
  }
  :deep(.custom-image-prev) {
    height: 168px !important;
  }
}
</style>
