import { ref, reactive, onMounted, computed, nextTick } from 'vue'

import { watchApi } from '@/service/index/watch'
import { useUserStore } from '@/store/user'
import { analyzeArray, getDataZoom, getHealthLevel, getLast10Data, sortLogData } from './shared'
import dayjs from 'dayjs'
import { hexToRgb } from '@/utils/index'
const useChart = () => {
  console.log('useChart')
}
// Define interfaces for API responses
export interface HealthDataItem {
  id: string
  user_id: string
  heart_rate: number
  blood_oxygen: number
  temperature: number
  systolic_pressure: number
  diastolic_pressure: number
  created_at: string
  updated_at: string
}

export interface HealthHistoryResponse {
  list?: HealthDataItem[]
  total?: number
  mood_data?: {
    deep_mood: number
    light_mood: number
    awake: number
    dates: string[]
  }
  vital_index?: {
    activity: number
    exercise: number
    rest: number
  }
  heart_index?: {
    hrv: number
    pressure: number
    body_battery: number
    blood_oxygen: number
  }
}

// Define types for the health data
export interface HealthData {
  heartRate: {
    log: { [key: string]: string }
    time: string
    min: number
    max: number
    real: number
    range: string
  }
  bloodOxygen: {
    log: { [key: string]: string }
    time: string

    min: number
    real: number
    range: string
  }
  temperature: {
    log: { [key: string]: string }
    time: string
    min: number
    max: number
    real: number
    range: string
  }
  bloodPressure: {
    dbp: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
    sbp: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
  }
  mood: {
    log: { [key: string]: string }
    time: string
    dates: string[]
  }
  psychology: {
    colors: string[]
    dates: string[]
    pressure: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
    anxiety: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
    depression: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
  }
  physiology: {
    name: string[]
    color: string[]
    af: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
    sa: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
    bv: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      range: string
    }
  }
  time: string[]
}
export interface HistoryHealthData {
  heartRate: {
    log: { [key: string]: string }
    time: string
    min: number
    max: number
    minV?: number
    maxV?: number
    real: number
    range: string
    less: number
    over: number
    avg?: number
  }
  bloodOxygen: {
    log: { [key: string]: string }
    time: string

    minV?: number
    min: number
    real: number
    range: string
    less: number

    avg?: number
  }
  temperature: {
    log: { [key: string]: string }
    time: string
    minV?: number
    maxV?: number
    min: number
    max: number
    real: number
    range: string
    less: number
    over: number
    avg?: number
  }
  bloodPressure: {
    dbp: {
      log: { [key: string]: string }
      time: string

      min: number
      max: number
      real: number
      range: string
      less: number
      over: number
      avg?: number
    }
    sbp: {
      log: { [key: string]: string }
      time: string

      min: number
      max: number
      real: number
      range: string
      less: number
      over: number
      avg?: number
    }
  }
  mood: {
    log: { [key: string]: string }
    time: string
    dates: string[]
    minV?: number
    maxV?: number
    avg?: number
  }
  psychology: {
    colors: string[]
    dates: string[]

    pressure: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      minV?: number
      maxV?: number
      avg?: number
      real: number
      range: string
    }
    anxiety: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      minV?: number
      maxV?: number
      avg?: number
      real: number
      range: string
    }
    depression: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      minV?: number
      avg?: number
      maxV?: number
      real: number
      range: string
    }
  }
  physiology: {
    name: string[]
    color: string[]
    af: {
      log: { [key: string]: string }
      time: string
      min: number
      avg?: number
      max: number
      minV?: number
      maxV?: number
      real: number
      range: string
    }
    sa: {
      log: { [key: string]: string }
      time: string
      min: number
      max: number
      real: number
      avg?: number
      range: string
      minV?: number
      maxV?: number
    }
    bv: {
      log: { [key: string]: string }
      time: string
      min: number
      avg?: number
      max: number
      minV?: number
      maxV?: number
      real: number
      range: string
    }
  }
  time: string[]
}

export const useHealthCharts = () => {
  const userStore = useUserStore()
  // Create individual refs instead
  const heartRateRef = ref(null)
  const bloodOxygenRef = ref(null)
  const temperatureRef = ref(null)
  const bloodPressureRef = ref(null)
  const heartHistoryRef = ref(null)
  const bloodOxygenHistoryRef = ref(null)
  const temperatureHistoryRef = ref(null)
  const bloodPressureHistoryRef = ref(null)
  const moodRef = ref(null)
  const psychologyRef = ref(null)
  const physiologyRef = ref(null)
  const psychologyLineRef = ref(null)
  const physiologyLineRef = ref(null)

  const healthData = reactive<HealthData>({
    heartRate: {
      log: {},
      min: 0,
      max: 0,
      real: 0,
      range: '',
      time: '',
    },
    bloodOxygen: {
      log: {},

      min: 0,
      real: 0,
      range: '',
      time: '',
    },

    temperature: {
      log: {},
      min: 0,
      max: 0,
      real: 0,
      range: '',
      time: '',
    },

    bloodPressure: {
      dbp: {
        log: {},
        min: 0,
        max: 0,
        real: 0,
        range: '',
        time: '',
      },
      sbp: {
        log: {},
        min: 0,
        max: 0,
        real: 0,
        range: '',
        time: '',
      },
    },

    mood: {
      log: {},
      time: '',
      dates: ['愤怒', '平静', '惊讶', '恐惧', '悲伤', '厌恶', '愉悦'],
    },
    psychology: {
      colors: ['#78C06E', '#409EFF', '#38C1B4'],
      dates: ['压力', '焦虑', '抑郁'],
      pressure: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      anxiety: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      depression: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
    },
    physiology: {
      name: ['房颤', '心率不齐', '血液粘稠度'],
      color: ['#FF9A0F', '#E4C477', '#F581AE'],
      af: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      sa: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      bv: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
    },

    time: ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00'],
  })
  const historyHealthData = reactive<HistoryHealthData>({
    heartRate: {
      log: {},
      min: 0,
      max: 0,
      real: 0,
      range: '',
      time: '',
      less: 0,
      over: 0,
      avg: 0,
    },
    bloodOxygen: {
      log: {},

      min: 0,
      real: 0,
      range: '',
      time: '',
      less: 0,

      avg: 0,
    },

    temperature: {
      log: {},
      min: 0,
      max: 0,
      real: 0,
      range: '',
      time: '',
      less: 0,
      over: 0,
      avg: 0,
    },

    bloodPressure: {
      dbp: {
        log: {},
        min: 0,
        max: 0,
        real: 0,
        range: '',
        time: '',
        less: 0,
        over: 0,
        avg: 0,
      },
      sbp: {
        log: {},
        min: 0,
        max: 0,
        real: 0,
        range: '',
        time: '',
        less: 0,
        over: 0,
        avg: 0,
      },
    },

    mood: {
      log: {},
      time: '',
      dates: ['愤怒', '平静', '惊讶', '恐惧', '悲伤', '厌恶', '愉悦'],
    },
    psychology: {
      colors: ['#78C06E', '#409EFF', '#38C1B4'],
      dates: ['压力', '焦虑', '抑郁'],
      pressure: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      anxiety: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      depression: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
    },
    physiology: {
      name: ['房颤', '心率不齐', '血液粘稠度'],
      color: ['#FF9A0F', '#E4C477', '#F581AE'],
      af: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      sa: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
      bv: {
        log: {},
        time: '',
        min: 0,
        max: 0,
        real: 0,
        range: '',
      },
    },

    time: ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00'],
  })

  // Chart options
  const heartRateOptions = computed(() => {
    const { keys, values } = getLast10Data(healthData.heartRate.log)

    return {
      grid: {
        top: 10,
        left: 10,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => item.split(':')[0] + ':' + item.split(':')[1]),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
          showMaxLabel: true,
          showMinLabel: true,
          hideOverlap: true,
          interval: function (index, value) {
            return index === 0 || index === keys.length - 1
          },
        },
      },
      yAxis: {
        show: false,
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 10,
        },
      },
      series: [
        {
          data: values,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          label: {
            show: false,
            position: 'top',
          },
          itemStyle: {
            color: '#fff',
            borderColor: '#F56C6C',
            borderWidth: 2,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(245, 108, 108, 0.4)',
                },
                {
                  offset: 1,
                  color: 'rgba(245, 108, 108, 0.1)',
                },
              ],
            },
          },
          lineStyle: {
            width: 2,
            color: '#F56C6C',
          },
        },
      ],
    }
  })

  const bloodOxygenOptions = computed(() => {
    const { keys, values } = getLast10Data(healthData.bloodOxygen.log)
    return {
      grid: {
        top: 10,
        left: 10,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => item.split(':')[0] + ':' + item.split(':')[1]),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
          showMaxLabel: true,
          showMinLabel: true,
          hideOverlap: true,
          interval: function (index, value) {
            return index === 0 || index === keys.length - 1
          },
        },
      },
      yAxis: {
        show: false,
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 10,
        },
      },
      series: [
        {
          data: values,
          type: 'bar',
          barWidth: 10,
          label: {
            show: false,
            position: 'top',
          },
          itemStyle: {
            color: '#F56C6C',
            borderRadius: [4, 4, 0, 0],
          },
        },
      ],
    }
  })

  const temperatureOptions = computed(() => {
    const { keys, values } = getLast10Data(healthData.temperature.log)
    return {
      grid: {
        top: 10,
        left: 10,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => item.split(':')[0] + ':' + item.split(':')[1]),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
          showMaxLabel: true,
          showMinLabel: true,
          hideOverlap: true,
          interval: function (index, value) {
            return index === 0 || index === keys.length - 1
          },
        },
      },
      yAxis: {
        show: false,
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 10,
        },
      },
      series: [
        {
          data: values,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          label: {
            show: false,
            position: 'top',
          },
          itemStyle: {
            color: '#fff',
            borderColor: 'rgba(103, 194, 58, 1)',
            borderWidth: 2,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.4)',
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)',
                },
              ],
            },
          },
          lineStyle: {
            width: 2,
            color: '#67C23A',
          },
        },
      ],
    }
  })

  const bloodPressureOptions = computed(() => {
    const { keys, values } = getLast10Data(healthData.bloodPressure.dbp.log)
    const { keys: keys2, values: values2 } = getLast10Data(healthData.bloodPressure.sbp.log)
    return {
      grid: {
        top: 10,
        left: 10,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => item.split(':')[0] + ':' + item.split(':')[1]),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
          showMaxLabel: true,
          showMinLabel: true,
          hideOverlap: true,
          interval: function (index, value) {
            return index === 0 || index === keys.length - 1
          },
        },
      },
      yAxis: [
        {
          show: false,
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
      ],
      series: [
        {
          name: '收缩压',
          data: values,
          type: 'line',
          smooth: true,
          label: {
            show: false,
            position: 'top',
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#fff',
            borderColor: '#F56C6C',
            borderWidth: 2,
          },
          lineStyle: {
            width: 2,
            color: '#F56C6C',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(245, 108, 108, 0.4)',
                },
                {
                  offset: 0.5,
                  color: 'rgba(245, 108, 108, 0.1)',
                },
              ],
            },
          },
        },
        {
          name: '舒张压',
          data: values2,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#fff',
            borderColor: '#E6A23C',
            borderWidth: 2,
          },
          lineStyle: {
            width: 2,
            color: '#E6A23C',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(255, 204, 134, 0.4)',
                },
                {
                  offset: 1,
                  color: 'rgba(255, 204, 134, 0.1)',
                },
              ],
            },
          },
        },
      ],
    }
  })

  const moodOptions = computed(() => {
    const data = healthData.mood.log || []
    const current = data[dayjs(healthData.mood.time).format('HH:mm:ss')] || []

    return {
      grid: {
        top: 30,
        left: 10,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: healthData.mood.dates,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      yAxis: {
        show: false,
        type: 'value',
      },
      series: [
        {
          type: 'bar',
          stack: 'mood',
          barWidth: 10,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
            color: '#333',
            fontSize: 12,
            fontWeight: 'bold',
            padding: [0, 0, 0, 10],
          },
          itemStyle: {
            color: '#38C1B4',
            borderRadius: [4, 4, 0, 0],
          },
          data: current.map((item) => item * 100).map((item) => item.toFixed(2)),
        },
      ],
    }
  })
  const psychologyOptions = computed(() => {
    return {
      legend: {
        orient: 'vertical',
        left: '2%',
        top: 'center',
        itemWidth: 0,
        itemHeight: 0,
        itemGap: 15,
        selectedMode: true,
        data: [
          {
            name: healthData.psychology.dates[0],
            icon: 'rect',
          },
          {
            name: healthData.psychology.dates[1],
            icon: 'rect',
          },
          {
            name: healthData.psychology.dates[2],
            icon: 'rect',
          },
        ],
        formatter: function (name) {
          // 找到当前项的数据和颜色
          const index = healthData.psychology.dates.findIndex((item) => item === name)

          const value =
            healthData.psychology.dates[index] === '压力'
              ? healthData.psychology.pressure.real
              : healthData.psychology.dates[index] === '焦虑'
                ? healthData.psychology.anxiety.real
                : healthData.psychology.depression.real
          const color = healthData.psychology.colors[index] || '#ccc'

          // 创建类似图片中的富文本样式
          return [
            '{colorBlock|} ',
            `{nameTxt|${name}}`,
            `{valueTxt|${value}} `,
            // `{levelTxt|(${getHealthLevel(value)}})`
          ].join('')
        },
        textStyle: {
          rich: {
            colorBlock: {
              width: 16,
              height: 14,
              borderRadius: 2,
              backgroundColor: function (params) {
                // 根据名称查找对应的颜色
                const index = healthData.psychology.dates.findIndex((item) => item === params.name)
                return healthData.psychology.colors[index] || '#ccc'
              },
            },
            nameTxt: {
              padding: [0, 5, 0, 5],
              color: '#666',
              fontSize: 14,
            },
            valueTxt: {
              color: '#333',
              fontSize: 14,
              fontWeight: 'bold',
              padding: [0, 0, 0, 10],
            },
          },
        },
      },

      series: [
        {
          name: '生理指标',
          type: 'pie',
          radius: [10, 72],
          center: ['75%', '50%'],
          roseType: 'area',
          clockwise: true, // 顺时针方向
          startAngle: 90, // 从12点钟方向开始
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 3,
          },
          label: {
            show: false,
          },
          data: [
            {
              value: healthData.psychology.pressure.real,
              name: healthData.psychology.dates[0],
              itemStyle: {
                color: healthData.psychology.colors[0],
              },
            },
            {
              value: healthData.psychology.anxiety.real,
              name: healthData.psychology.dates[1],
              itemStyle: {
                color: healthData.psychology.colors[1],
              },
            },
            {
              value: healthData.psychology.depression.real,
              name: healthData.psychology.dates[2],
              itemStyle: {
                color: healthData.psychology.colors[2],
              },
            },
          ],
        },
      ],
    }
  })

  const physiologyOptions = computed(() => {
    const { values: afValues } = getLast10Data(healthData.physiology.af.log)
    const { values: saValues } = getLast10Data(healthData.physiology.sa.log)
    const { values: bvValues } = getLast10Data(healthData.physiology.bv.log)
    return {
      tooltip: {},
      color: healthData.physiology.color,
      legend: {
        show: false,
        orient: 'vertical',
        left: '2%',
        top: 'center',
        itemWidth: 0,
        itemHeight: 0,
        itemGap: 15,
        selectedMode: true,
        data: [
          {
            name: healthData.physiology.name[0],
            icon: 'rect',
          },
          {
            name: healthData.physiology.name[1],
            icon: 'rect',
          },
          {
            name: healthData.physiology.name[2],
            icon: 'rect',
          },
        ],
        formatter: function (name) {
          // 找到当前项的数据和颜色
          const index = healthData.physiology.name.findIndex((item) => item === name)
          const value =
            index === 0
              ? healthData.physiology.af.real
              : index === 1
                ? healthData.physiology.sa.real
                : healthData.physiology.bv.real

          // 创建类似图片中的富文本样式
          return [
            '{colorBlock|} ',
            `{nameTxt|${name}}`,
            `{valueTxt|${value || 0}}`,
            // `{levelTxt|(${getHealthLevel(value)}})`
          ].join('')
        },
        textStyle: {
          rich: {
            colorBlock: {
              width: 16,
              height: 14,
              borderRadius: 2,
              backgroundColor: function (params) {
                // 根据名称查找对应的颜色
                const index = healthData.physiology.name.findIndex((item) => item === params.name)
                return healthData.physiology.color[index] || '#ccc'
              },
            },
            nameTxt: {
              padding: [0, 5, 0, 5],
              color: '#666',
              fontSize: 14,
            },
            valueTxt: {
              color: '#333',
              fontSize: 14,
              fontWeight: 'bold',
              padding: [0, 0, 0, 10],
            },
          },
        },
      },
      radar: {
        indicator: healthData.physiology.name.map((item) => ({
          name: item,
          // max:100
        })),
        center: ['50%', '60%'],
        radius: 65,
        startAngle: 90,
        splitNumber: 4,
        shape: 'circle',

        axisLine: {
          lineStyle: {
            color: 'rgba(211, 253, 250, 0.8)',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(211, 253, 250, 0.8)',
          },
        },
        splitArea: {
          areaStyle: {
            color: ['#77EADF', '#26C3BE', '#64AFE9', '#428BD4'],
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: 10,
          },
        },
        name: {
          show: true,
          color: (params) => {
            if (params.name === '房颤') {
              return healthData.physiology.color[0]
            } else if ((params.name = '心率不齐')) {
              return healthData.physiology.color[1]
            } else if (params.name === '血液粘稠度') {
              return healthData.physiology.color[2]
            }
            return '#333'
          },
          fontSize: 14,
        },
      },
      series: [
        {
          name: '心理指标',
          type: 'radar',
          emphasis: {
            lineStyle: {
              width: 4,
            },
          },
          data: [
            {
              value: [
                healthData.physiology.af.real,
                healthData.physiology.sa.real,
                healthData.physiology.bv.real,
              ],
              name: healthData.physiology.name[0],
              label: {
                show: true,
                position: 'top',
                formatter: '{c}',
                color: '#000',
                fontWeight: 'bold',
                fontSize: 14,
              },
            },
            // {
            //   value: saValues,
            //   name: healthData.physiology.name[1],
            //   itemStyle: {
            //     color: healthData.physiology.color[1]
            //   }
            // },
            // {
            //   value: bvValues,
            //   name: healthData.physiology.name[2],
            //   itemStyle: {
            //     color: healthData.physiology.color[2]
            //   }
            // }
          ],
        },
      ],
    }
  })
  // 历史数据折线图
  const getLineOptions = ({ log = {}, min, max }: HistoryHealthData['heartRate']) => {
    // Get min and max values
    const { keys, values } = sortLogData(log)

    const minValue = min
    const maxValue = max
    console.log(getDataZoom(keys))
    return {
      ...getDataZoom(keys),

      grid: {
        top: 20,
        left: 30,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => dayjs(item).format('HH:mm')),
        axisLine: {
          show: false,
        },

        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      yAxis: {
        show: true,
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
        },
        axisLabel: {
          color: '#999',
          fontSize: 10,
        },
      },
      series: [
        {
          data: values.map((value) => ({
            value,
            label: {
              show: true,
              position: 'top',
              color:
                Number(value) >= maxValue
                  ? '#F56C6C'
                  : Number(value) <= minValue
                    ? '#409EFF'
                    : '#ccc',
            },
          })),
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#fff',
            borderColor: '#F56C6C',
            borderWidth: 2,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(245, 108, 108, 0.4)',
                },
                {
                  offset: 1,
                  color: 'rgba(245, 108, 108, 0.1)',
                },
              ],
            },
          },
          lineStyle: {
            width: 2,
            color: '#F56C6C',
          },
        },
      ],
    }
  }
  // 历史血压折线图
  const getBloodPressureLineOptions = ({
    dbp: { log: dbpLog = {}, min: dbpMin, max: dbpMax },
    sbp: { log: sbpLog = {}, min: sbpMin, max: sbpMax },
  }: {
    dbp: any
    sbp: any
  }) => {
    const { keys: dbpKeys, values: dbpValues } = sortLogData(dbpLog)
    const { keys: sbpKeys, values: sbpValues } = sortLogData(sbpLog)
    const minDbp = Number(dbpMin)
    const maxDbp = Number(dbpMax)
    const minSbp = Number(sbpMin)
    const maxSbp = Number(sbpMax)

    return {
      ...getDataZoom(dbpKeys),
      grid: {
        top: 30,
        left: 30,
        right: 0,

        bottom: 20,
      },
      legend: {
        show: false,
        data: ['收缩压', '舒张压'],
        top: '0%',
        right: 0,
      },
      xAxis: {
        type: 'category',
        data: dbpKeys.map((item) => dayjs(item).format('HH:mm')),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      yAxis: [
        {
          show: true,
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
      ],
      series: [
        {
          name: '收缩压',
          data: dbpValues.map((value) => ({
            value,
            label: {
              show: true,
              position: 'top',
              color:
                Number(value) >= maxDbp ? '#F56C6C' : Number(value) <= minDbp ? '#409EFF' : '#ccc',
            },
          })),
          type: 'line',
          smooth: true,

          symbol: 'circle',
          symbolSize: 6,

          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(245, 108, 108, 0.4)',
                },
                {
                  offset: 0.5,
                  color: 'rgba(245, 108, 108, 0.1)',
                },
              ],
            },
          },
        },
        {
          name: '舒张压',
          data: sbpValues.map((value) => ({
            value,
            label: {
              show: true,
              position: 'top',
              color:
                Number(value) >= maxSbp ? '#F56C6C' : Number(value) <= minSbp ? '#409EFF' : '#ccc',
            },
          })),
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,

          itemStyle: {
            color: '#fff',
            borderColor: '#E6A23C',
            borderWidth: 2,
          },
          lineStyle: {
            width: 2,
            color: '#E6A23C',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(255, 204, 134, 0.4)',
                },
                {
                  offset: 1,
                  color: 'rgba(255, 204, 134, 0.1)',
                },
              ],
            },
          },
        },
      ],
    }
  }
  // 历史情绪折现图
  const getMoodLineOptions = ({ log = {}, dates = [] }: any, moodTab: number) => {
    const { keys, values } = sortLogData(log)
    console.log(log, dates, moodTab)
    return {
      ...getDataZoom(keys),
      grid: {
        top: 30,
        left: 40,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => dayjs(item).format('HH:mm')),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      yAxis: {
        show: true,

        type: 'value',
      },
      series: [
        {
          data: values.map((item) => item[moodTab] * 100),
          type: 'line',
          stack: 'mood',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          barWidth: 10,
          label: {
            show: true,
            position: 'top',
            color: '#999',
            fontSize: 12,
            formatter: (params: any) => {
              return Number(params.value).toFixed(2) + '%'
            },
          },
          itemStyle: {
            color: '#38C1B4',
            borderRadius: [4, 4, 0, 0],
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(56, 193, 180, 0.4)',
                },
              ],
            },
          },
        },
      ],
    }
  }
  // 历史心理折线图
  const getPsychologyLineOptions = (data: HistoryHealthData['psychology'], tab: number) => {
    const { pressure, anxiety, depression, colors } = data
    const { keys: pressureKeys, values: pressureValues } = sortLogData(pressure.log)
    const { keys: anxietyKeys, values: anxietyValues } = sortLogData(anxiety.log)
    const { keys: depressionKeys, values: depressionValues } = sortLogData(depression.log)

    const keys = tab === 0 ? pressureKeys : tab === 1 ? anxietyKeys : depressionKeys
    const values = tab === 0 ? pressureValues : tab === 1 ? anxietyValues : depressionValues
    const { r, g, b } = hexToRgb(colors[tab])
    return {
      ...getDataZoom(pressureKeys),
      grid: {
        top: 30,
        left: 30,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => dayjs(item).format('HH:mm')),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      yAxis: {
        show: true,
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      series: [
        {
          data: values,
          type: 'line',
          stack: 'mood',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          barWidth: 10,
          label: {
            show: true,
            position: 'top',
            color: '#999',
            fontSize: 12,
          },
          itemStyle: {
            color: colors[tab],
            borderRadius: [4, 4, 0, 0],
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: `rgba(${r}, ${g}, ${b}, 0.4)`,
                },
              ],
            },
          },
        },
      ],
    }
  }
  // 历史生理折线图
  const getPhysiologyLineOptions = (data: HistoryHealthData['physiology'], tab: number) => {
    const { af, sa, bv, color } = data

    const { keys: afKeys, values: afValues } = sortLogData(af.log)
    const { keys: saKeys, values: saValues } = sortLogData(sa.log)
    const { keys: bvKeys, values: bvValues } = sortLogData(bv.log)
    const values = tab === 0 ? afValues : tab === 1 ? saValues : bvValues
    const keys = tab === 0 ? afKeys : tab === 1 ? saKeys : bvKeys
    const { r, g, b } = hexToRgb(color[tab])
    return {
      ...getDataZoom(afKeys),
      grid: {
        top: 30,
        left: 30,
        right: 10,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: keys.map((item) => dayjs(item).format('HH:mm')),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      yAxis: {
        show: true,
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
        },
      },
      series: [
        {
          data: values,
          type: 'line',
          stack: 'mood',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          barWidth: 10,
          label: {
            show: true,
            position: 'top',
            color: '#999',
            fontSize: 12,
          },
          itemStyle: {
            color: color[tab],
            borderRadius: [4, 4, 0, 0],
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: `rgba(${r}, ${g}, ${b}, 0.4)`,
                },
              ],
            },
          },
        },
      ],
    }
  }

  // Initialize charts
  const initCharts = () => {
    console.log('Initializing charts...')

    // Wait for next tick to ensure DOM is ready
    nextTick(() => {
      try {
        // Heart rate chart
        if (heartRateRef.value) {
          console.log('Initializing heart rate chart with element:', heartRateRef.value)
          //   useChart(heartRateRef, heartRateOptions.value)
        } else {
          console.warn('Heart rate chart ref is not available')
        }

        // Blood oxygen chart
        if (bloodOxygenRef.value) {
          console.log('Initializing blood oxygen chart')
          //   useChart(bloodOxygenRef, bloodOxygenOptions.value)
        }

        // Temperature chart
        if (temperatureRef.value) {
          console.log('Initializing temperature chart')
          //   useChart(temperatureRef, temperatureOptions.value)
        }

        // Blood pressure chart
        if (bloodPressureRef.value) {
          console.log('Initializing blood pressure chart')
          //   useChart(bloodPressureRef, bloodPressureOptions.value)
        }

        // Mood chart
        if (moodRef.value) {
          console.log('Initializing mood chart')
          //   useChart(moodRef, moodOptions.value)
        }

        // Vital index chart
        if (psychologyRef.value) {
          console.log('Initializing vital index chart')
          //   useChart(psychologyRef, psychologyOptions.value)
        }

        // Heart index chart
        if (physiologyRef.value) {
          console.log('Initializing heart index chart')
          //   useChart(physiologyRef, physiologyOptions.value)
        }
      } catch (error) {
        console.error('Error initializing charts:', error)
      }
    })
  }
  const initHistoryCharts = (tab = 0) => {
    nextTick(() => {
      try {
        // Heart rate chart
        if (heartHistoryRef.value) {
          console.log('Initializing heart rate chart with element:', heartHistoryRef.value)
          //   useChart(heartHistoryRef, getLineOptions(historyHealthData.heartRate))
        } else {
          console.warn('Heart rate chart ref is not available')
        }

        // // Blood oxygen chart
        if (bloodOxygenHistoryRef.value) {
          console.log('Initializing blood oxygen chart')
          //   useChart(bloodOxygenHistoryRef, getLineOptions(historyHealthData.bloodOxygen as any))
        }

        // // Temperature chart
        if (temperatureHistoryRef.value) {
          console.log('Initializing temperature chart')
          //   useChart(temperatureHistoryRef, getLineOptions(historyHealthData.temperature))
        }

        // // Blood pressure chart
        if (bloodPressureHistoryRef.value) {
          console.log('Initializing blood pressure chart')
          //   useChart(
          //     bloodPressureHistoryRef,
          //     getBloodPressureLineOptions(historyHealthData.bloodPressure),
          //   )
        }

        // // Mood chart
        if (moodRef.value) {
          console.log('Initializing mood chart')
          //   useChart(moodRef, getMoodLineOptions(historyHealthData.mood, tab))
        }

        // // Vital index chart
        if (psychologyLineRef.value) {
          console.log('Initializing vital index chart')
          //   useChart(psychologyLineRef, getPsychologyLineOptions(historyHealthData.psychology, tab))
        }

        // // Heart index chart
        if (physiologyLineRef.value) {
          console.log('Initializing heart index chart')
          //   useChart(physiologyLineRef, getPhysiologyLineOptions(historyHealthData.physiology, tab))
        }
      } catch (error) {
        console.error('Error initializing charts:', error)
      }
    })
  }

  //  最近的数据
  const fetchRecentHealthData = async () => {
    try {
      const userData = await watchApi.surveyWatchGetUserInfoCreate({
        // user_id: userStore.userInfo.user_id,
        user_id: '20240425000712',
        start_date: dayjs().format('YYYY/MM/DD'),
        end_date: dayjs().format('YYYY/MM/DD'),
      })

      if (userData && userData.data) {
        // Process the data (this part will depend on your API response structure)
        const responseData = userData.data as any
        const { hr, spo2, tp, bp, emoratio, pressure, depression, anxiety, af, sa, bv } =
          responseData
        if (hr) {
          // 心率
          const { hr_log, hr_min, hr_max, real_hr, range_hr, gather_time } = hr

          healthData.heartRate = {
            log: hr_log,
            min: hr_min,
            max: hr_max,
            real: real_hr,
            range: range_hr,
            time: gather_time,
          }
        }
        if (spo2) {
          // 血氧
          const { spo2_log, spo2_min, real_spo2, range_spo2, gather_time } = spo2

          healthData.bloodOxygen = {
            log: spo2_log,
            min: spo2_min,
            real: real_spo2,
            range: range_spo2,
            time: gather_time,
          }
        }
        if (tp) {
          // 体温
          const { tp_log, tp_min, tp_max, real_tp, range_tp, gather_time } = tp
          healthData.temperature = {
            log: tp_log,
            min: tp_min,
            max: tp_max,
            real: real_tp,
            range: range_tp,
            time: gather_time,
          }
        }
        if (bp) {
          // 血压
          const {
            dbp_log,
            dbp_max,
            dbp_min,
            range_dbp,
            real_dbp,
            sbp_log,
            sbp_max,
            sbp_min,
            range_sbp,
            real_sbp,
            gather_time,
          } = bp
          healthData.bloodPressure = {
            dbp: {
              log: dbp_log,
              min: dbp_min,
              max: dbp_max,
              real: real_dbp,
              range: range_dbp,
              time: gather_time,
            },
            sbp: {
              log: sbp_log,
              min: sbp_min,
              max: sbp_max,
              real: real_sbp,
              range: range_sbp,
              time: gather_time,
            },
          }
        }
        if (emoratio) {
          const { emoRatio_log, gather_time } = emoratio
          healthData.mood = {
            log: emoRatio_log,
            time: gather_time,
            dates: ['愤怒', '平静', '惊讶', '恐惧', '悲伤', '厌恶', '愉悦'],
          }
        }
        if (depression) {
          // Safely destructure depression data with defaults
          const {
            depression_log = {},
            gather_time = '',
            real_depression = 0,
            range_depression = 0,
          } = depression || {}
          const current = depression_log?.[dayjs(gather_time || '').format('HH:mm:ss')] || []

          // Safely destructure pressure data with defaults
          const {
            pressure_log = {},
            gather_time: pressure_time = '',
            real_pressure = 0,
            range_pressure = 0,
          } = pressure || {}
          const pressureCurrent =
            pressure_log?.[dayjs(pressure_time || '').format('HH:mm:ss')] || []

          // Safely destructure anxiety data with defaults
          const {
            anxiety_log = {},
            gather_time: anxiety_time = '',
            real_anxiety = 0,
            range_anxiety = 0,
          } = anxiety || {}
          const anxietyCurrent = anxiety_log?.[dayjs(anxiety_time || '').format('HH:mm:ss')] || []
          healthData.psychology = {
            colors: ['#78C06E', '#409EFF', '#38C1B4'],
            dates: ['压力', '焦虑', '抑郁'],
            pressure: {
              log: pressureCurrent,
              time: pressure_time,
              min: 0,
              max: 0,
              real: real_pressure,
              range: range_pressure,
            },
            anxiety: {
              log: anxietyCurrent,
              time: anxiety_time,
              min: 0,
              max: 0,
              real: real_anxiety,
              range: range_anxiety,
            },
            depression: {
              log: current,
              time: gather_time,
              min: 0,
              max: 0,
              real: real_depression,
              range: range_depression,
            },
          }
        }
        if (af) {
          const { af_log, gather_time, real_af = 0, range_af = 0 } = af

          const { sa_log, gather_time: sa_time, real_sa = 0, range_sa = 0 } = sa

          const { bv_log, gather_time: bv_time, real_bv, range_bv } = bv

          healthData.physiology = {
            name: ['房颤', '心率不齐', '血液粘稠度'],
            color: ['#FF9A0F', '#E4C477', '#F581AE'],
            af: {
              log: af_log,
              time: gather_time,
              min: 0,
              max: 0,
              real: real_af || 0,
              range: range_af,
            },
            sa: {
              log: sa_log,
              time: sa_time,
              min: 0,
              max: 0,
              real: real_sa || 0,
              range: range_sa,
            },
            bv: {
              log: bv_log,
              time: bv_time,
              min: 0,
              max: 0,
              real: real_bv || 0,
              range: range_bv,
            },
          }
        }

        // initCharts()
      }
    } catch (error) {
      console.error('Error fetching health data:', error)
    }
  }
  const getHistoryData = (start_date: string, end_date: string, moodTab: number) => {
    watchApi
      .surveyWatchGetUserHistoryInfoCreate({
        // user_id: userStore.userInfo.user_id,
        user_id: '20240425000712',
        start_date: start_date,
        end_date: end_date,
      })
      .then((res) => {
        const { hr, spo2, tp, bp, emoratio, pressure, depression, anxiety, af, sa, bv } =
          res.data as any
        if (hr) {
          // 心率
          const {
            hr_log = {},
            hr_min,
            hr_max,
            real_hr,
            range_hr,
            gather_time,
            average_hr,
            less_hr,
            over_hr,
          } = hr
          const { min, max } = analyzeArray(Object.values(hr_log))

          historyHealthData.heartRate = {
            log: hr_log,
            time: gather_time,
            min: min,
            minV: min,
            max: max,
            maxV: max,
            less: less_hr,
            over: over_hr,
            avg: average_hr,
            real: real_hr,
            range: range_hr,
          }
        }
        if (spo2) {
          // 血氧
          const {
            spo2_log = {},
            spo2_min,
            real_spo2,
            range_spo2,
            gather_time,
            average_spo2,
            less_spo2,
          } = spo2
          const { min, max } = analyzeArray(Object.values(spo2_log))

          historyHealthData.bloodOxygen = {
            log: spo2_log,
            time: gather_time,
            min: spo2_min,
            minV: min,

            real: real_spo2,
            range: range_spo2,
            less: less_spo2,

            avg: average_spo2,
          }
        }
        if (tp) {
          // 体温
          const {
            tp_log = {},
            tp_min,
            tp_max,
            real_tp,
            range_tp,
            gather_time,
            average_tp,
            less_tp,
            over_tp,
          } = tp
          const { min, max } = analyzeArray(Object.values(tp_log))

          historyHealthData.temperature = {
            log: tp_log,
            time: gather_time,
            min: tp_min,
            maxV: max,
            minV: min,
            max: tp_max,
            real: real_tp,
            range: range_tp,
            less: less_tp,
            over: over_tp,
            avg: average_tp,
          }
        }
        if (bp) {
          // 血压
          const {
            dbp_log,
            dbp_max,
            dbp_min,
            range_dbp,
            real_dbp,
            sbp_log,
            sbp_max,
            sbp_min,
            range_sbp,
            real_sbp,
            gather_time,
            average_dbp,
            less_dbp,
            over_dbp,
            average_sbp,
            less_sbp,
            over_sbp,
          } = bp
          historyHealthData.bloodPressure = {
            dbp: {
              log: dbp_log,
              time: gather_time,
              min: dbp_min,
              max: dbp_max,
              real: real_dbp,
              range: range_dbp,
              less: less_dbp,
              over: over_dbp,
              avg: average_dbp,
            },
            sbp: {
              log: sbp_log,
              time: gather_time,
              min: sbp_min,
              max: sbp_max,
              real: real_sbp,
              range: range_sbp,
              less: less_sbp,
              over: over_sbp,
              avg: average_sbp,
            },
          }
          console.log(historyHealthData.bloodPressure)
        }
        if (emoratio) {
          const { emoRatio_log, gather_time } = emoratio
          historyHealthData.mood = {
            log: emoRatio_log,
            time: gather_time,
            dates: ['愤怒', '平静', '惊讶', '恐惧', '悲伤', '厌恶', '愉悦'],
            minV: 0,
            maxV: 0,
            avg: 0,
          }
        }
        if (depression) {
          const {
            depression_log = {},
            gather_time,
            real_depression,
            range_depression,
            average_depression,
          } = depression

          const {
            pressure_log = {},
            gather_time: pressure_time,
            real_pressure,
            range_pressure,
            average_pressure,
          } = pressure

          const {
            anxiety_log = {},
            gather_time: anxiety_time,
            real_anxiety,
            range_anxiety,
            average_anxiety,
          } = anxiety
          const { min: min_depression, max: max_depression } = analyzeArray(
            Object.values(depression_log),
          )

          const { min: min_pressure, max: max_pressure } = analyzeArray(Object.values(pressure_log))

          const { min: min_anxiety, max: max_anxiety } = analyzeArray(Object.values(anxiety_log))

          historyHealthData.psychology = {
            colors: ['#78C06E', '#409EFF', '#38C1B4'],
            dates: ['压力', '焦虑', '抑郁'],
            depression: {
              log: depression_log,
              time: gather_time,
              min: 0,
              max: 0,
              minV: min_depression,
              avg: average_depression,
              maxV: max_depression,
              real: real_depression,
              range: range_depression,
            },
            pressure: {
              log: pressure_log,
              time: pressure_time,
              min: 0,
              max: 0,
              minV: min_pressure,
              avg: average_pressure,
              maxV: max_pressure,
              real: real_pressure,
              range: range_pressure,
            },
            anxiety: {
              log: anxiety_log,
              time: anxiety_time,
              min: 0,
              max: 0,
              minV: min_anxiety,
              avg: average_anxiety,
              maxV: max_anxiety,
              real: real_anxiety,
              range: range_anxiety,
            },
          }
        }
        if (af) {
          const { af_log = {}, gather_time, real_af, range_af, average_af } = af

          const { sa_log = {}, gather_time: sa_time, real_sa, range_sa, average_sa } = sa

          const { bv_log = {}, gather_time: bv_time, real_bv, range_bv, average_bv } = bv

          const { min: min_af, max: max_af } = analyzeArray(Object.values(af_log))

          const { min: min_sa, max: max_sa } = analyzeArray(Object.values(sa_log))

          const { min: min_bv, max: max_bv } = analyzeArray(Object.values(bv_log))

          historyHealthData.physiology = {
            name: ['房颤', '心率不齐', '血液粘稠度'],
            color: ['#FF9A0F', '#E4C477', '#F581AE'],
            af: {
              log: af_log,
              time: gather_time,
              min: 0,
              max: 0,
              avg: average_af,
              minV: min_af,
              maxV: max_af,
              real: real_af,
              range: range_af,
            },
            sa: {
              log: sa_log,
              time: sa_time,
              min: 0,
              max: 0,
              avg: average_sa,
              minV: min_sa,
              maxV: max_sa,
              real: real_sa,
              range: range_sa,
            },
            bv: {
              log: bv_log,
              time: bv_time,
              min: 0,
              max: 0,
              avg: average_bv,
              minV: min_bv,
              maxV: max_bv,
              real: real_bv,
              range: range_bv,
            },
          }
        }
        console.log(historyHealthData)
        // initHistoryCharts(moodTab)
      })
  }

  onMounted(() => {
    // Wait until components are mounted before initializing
    nextTick(() => {
      initCharts()
    })
  })

  return {
    // Return individual refs
    getHistoryData,
    psychologyLineRef,
    physiologyLineRef,
    heartRateOptions,
    bloodOxygenOptions,
    temperatureOptions,
    bloodPressureOptions,
    moodOptions,
    psychologyOptions,
    physiologyOptions,
    initHistoryCharts,
    historyHealthData,
    heartRateRef,
    bloodOxygenRef,
    temperatureRef,
    bloodPressureRef,
    heartHistoryRef,
    bloodOxygenHistoryRef,
    temperatureHistoryRef,
    bloodPressureHistoryRef,
    moodRef,
    psychologyRef,
    physiologyRef,
    healthData,
    fetchRecentHealthData,
    initCharts,
    getLineOptions,
    getBloodPressureLineOptions,
    getMoodLineOptions,
    getPsychologyLineOptions,
    getPhysiologyLineOptions,
  }
}
