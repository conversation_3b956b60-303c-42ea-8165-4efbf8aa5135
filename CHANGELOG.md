## 变更日志

### [2.6.1](https://github.com/feige996/unibest/compare/v2.6.0...v2.6.1) (2025-01-08)

## 变更日志

### 🐛 Bug Fixes | Bug 修复

- 枚举失效问题 & 分离全局声明和模块化内容 ([f2ae977](https://github.com/feige996/unibest/commit/f2ae977e9a52a573176ec4490621cdf75877ecd5))
- 修复全局搜索组件汉化配置 ([f92ce9d](https://github.com/feige996/unibest/commit/f92ce9d4fae61a9109bfd9152cede95202ec857a))
- app-plus、dev/prod、nativeResources插件未被正确移 ([776da46](https://github.com/feige996/unibest/commit/776da46619d1324d7e490dc06b821e1ee9b97fd0))
- buildTime ([d300d2c](https://github.com/feige996/unibest/commit/d300d2ca04998bc2de5bb2e739f39db602707e13))
- env utils 方法名称拼写问题 ([0e77899](https://github.com/feige996/unibest/commit/0e77899084dd101de45a5aeee9b6d752ff8888af))
- env utils 方法名称拼写问题 ([f1b245d](https://github.com/feige996/unibest/commit/f1b245d66f292f37f47aa7a094a9ec566c98ce91))
- fivicon.ico 路径 ([658abf2](https://github.com/feige996/unibest/commit/658abf211cb3bcbf27a75a062a9ae2e675208b6d))
- logo.svg 路径 ([c7ad1dc](https://github.com/feige996/unibest/commit/c7ad1dc7cd5b22a2bfb828162f169ee1366e3535))
- miniProgram 需要区分不同的小程序 ([732f286](https://github.com/feige996/unibest/commit/732f286ac1b8b0cf084317a2cd0d09d4fad767df))
- navigateBack 不可拦截,导致BUG ([75ad9af](https://github.com/feige996/unibest/commit/75ad9af648284787f0874af9a9bf708ca6411d07))
- **package.json:** pnpm cz error ([435aed6](https://github.com/feige996/unibest/commit/435aed66bca2f0b12771c37ba4a21d8f5ccffb2d))
- **tsconfig:** 修复z-paging库的TS类型丢失 ([61fa2e7](https://github.com/feige996/unibest/commit/61fa2e773a87880467bdcd382ffdbcf188acb7ba))
- **tsconfig:** 修复z-paging库的TS类型丢失 ([c7b5e60](https://github.com/feige996/unibest/commit/c7b5e60fed2366c5005998ada1d0f46765bbc22b))

### ✨ Features | 新功能

- 把 docs 仓库移动到本仓库 ([b2bf1ef](https://github.com/feige996/unibest/commit/b2bf1efef313396d45bb8616d895806d4361692a))
- 路由拦截全部加上 ([c47eb87](https://github.com/feige996/unibest/commit/c47eb871322989b8b919e3ad4ed0294273572e88))
- 小程序标识有9种 ([1846349](https://github.com/feige996/unibest/commit/18463493a6a44bbd90e351ac6d0ed7c4e0f357eb))
- 优化文档 ([9995e8a](https://github.com/feige996/unibest/commit/9995e8af8c559b514f15ff59c45e951e3d75c9bc))
- add 小红书 dev和 build 命令 ([b52ea52](https://github.com/feige996/unibest/commit/b52ea52561e5ec2859ccf4095710e158ff5495ee))
- add standard-version ([05b4ca3](https://github.com/feige996/unibest/commit/05b4ca32724a6ac2679b92a400c6c3033023728a))
- hbx 模板地址更正 ([817ac0b](https://github.com/feige996/unibest/commit/817ac0b4459e4b68f226068e30e759b73162b517))
- version + base 优化 ([af45c69](https://github.com/feige996/unibest/commit/af45c69784f54f5da0ecd6c733d6faed08d5bc77))
- wot-ui 备用地址 ([e8807fe](https://github.com/feige996/unibest/commit/e8807fef0059907a2b83d1eb4af13701fdb2e69e))
